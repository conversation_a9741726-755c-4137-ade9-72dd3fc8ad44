.content {
  max-width: 1600px;
  width: 100%;
  margin: 0 auto;
  box-sizing: border-box;
  padding: 24px 0;
  height: 100%;
  overflow-y: auto;
  -ms-overflow-style: none;
  /* 适用于 Internet Explorer 和旧版 Edge */
  scrollbar-width: none;
  /* 适用于 Firefox */

  .content::-webkit-scrollbar {
    display: none;
    /* 隐藏滚动条 */
  }

  .item {
    width: 100%;
    height: 128px;
    background: #ffffff;
    border: 1px solid #dae1ed;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    // padding: 24px;
    transition: all 0.3s;
    cursor: pointer;
    border-radius: 4px;
    position: relative;

    &:hover {
      box-shadow: 0px 4px 16px 8px rgba(0, 0, 0, 0.04);
      transition: all 0.3s;
      transform: translateY(-5px);
    }

    .type {
      width: 344px;
      height: 22px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 22px;
      text-align: left;
      padding-left: 16px;
      font-size: 12px;
      box-sizing: border-box;
      position: absolute;
      top: 106px;
      left: 0;
      background: linear-gradient(90deg, #21b838 23%, rgba(33, 184, 56, 0.00) 100%);
    }

    .active-type {
      width: 344px;
      height: 22px;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 22px;
      text-align: left;
      padding-left: 16px;
      font-size: 12px;
      box-sizing: border-box;
      position: absolute;
      top: 106px;
      left: 0;
      background: linear-gradient(90deg, #315782 23%, rgba(49, 87, 130, 0.00) 100%);

    }

    .item-info {
      flex: 1;
      height: 90px;
      margin-left: 16px;

      .top {
        width: 100%;
        display: flex;
        align-items: center;
        height: 24px;



        .title {
          width: 966px;
          height: 24px;
          font-size: 16px;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.88);
          line-height: 24px;
          margin-left: 8px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          text-align: left;
        }
      }

      .record {
        width: 100%;
        height: 24px;
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
        line-height: 24px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        text-align: left;
        margin-top: 9px;
      }

      .progress {
        width: 336px;
        margin-top: 9px;
        height: 24px;
        display: flex;
        align-items: center;

        .tip {
          margin-left: 8px;
          height: 24px;
          font-size: 14px;
          font-weight: 400;
          color: #ff4d4f;
          line-height: 24px;
        }
      }
    }
  }
}

.extra {
  width: 1200px;
  margin: 0 auto;
  margin-top: 80px;
  text-align: center;
  height: 40px;
  font-size: 16px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.2);
  line-height: 40px;
}