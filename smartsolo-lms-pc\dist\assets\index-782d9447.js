import{r as c,_ as Y,a as w,R as Sn,b as ve,c as ne,d as ee,e as Ge,w as Re,f as Cn,g as En,u as je,h as tt,i as Gt,j as Yt,k as Rt,m as _t,l as er,n as Je,o as tr,P as nr,p as rr,q as ar,C as Dt,s as or,t as ir,v as sr,T as lr,x as cr,y as wt,K as Ae,z as He,A as dr,B as ur,D as _e,E as Nn,F as Qe,G as q,H as Kn,I as fr,J as vr,L as hr,M as F,N as We,O as gr,Q as pr,S as yr,U as mr,V as br,W as wn,X as K,Y as Jt,Z as st,$ as xr,a0 as Zt,a1 as Sr,a2 as Cr,a3 as Xe,a4 as Er,a5 as Qt,a6 as Nr,a7 as Kr}from"./index-d424f8d9.js";import{m as wr,E as kr,d as yt,a as mt,b as bt}from"./thumb3-b5583002.js";var kn=c.forwardRef(function(e,n){var r=e.height,t=e.offsetY,o=e.offsetX,s=e.children,u=e.prefixCls,a=e.onInnerResize,l=e.innerProps,i=e.rtl,f=e.extra,d={},h={display:"flex",flexDirection:"column"};return t!==void 0&&(d={height:r,position:"relative",overflow:"hidden"},h=Y(Y({},h),{},w(w(w(w(w({transform:"translateY(".concat(t,"px)")},i?"marginRight":"marginLeft",-o),"position","absolute"),"left",0),"right",0),"top",0))),c.createElement("div",{style:d},c.createElement(Sn,{onResize:function(y){var m=y.offsetHeight;m&&a&&a()}},c.createElement("div",ve({style:h,className:ne(w({},"".concat(u,"-holder-inner"),u)),ref:n},l),s,f)))});kn.displayName="Filler";function $r(e){var n=e.children,r=e.setRef,t=c.useCallback(function(o){r(o)},[]);return c.cloneElement(n,{ref:t})}function Or(e,n,r,t,o,s,u,a){var l=a.getKey;return e.slice(n,r+1).map(function(i,f){var d=n+f,h=u(i,d,{style:{width:t},offsetX:o}),v=l(i);return c.createElement($r,{key:v,setRef:function(m){return s(i,m)}},h)})}function Rr(e,n,r){var t=e.length,o=n.length,s,u;if(t===0&&o===0)return null;t<o?(s=e,u=n):(s=n,u=e);var a={__EMPTY_ITEM__:!0};function l(y){return y!==void 0?r(y):a}for(var i=null,f=Math.abs(t-o)!==1,d=0;d<u.length;d+=1){var h=l(s[d]),v=l(u[d]);if(h!==v){i=d,f=f||h!==l(u[d+1]);break}}return i===null?null:{index:i,multiple:f}}function _r(e,n,r){var t=c.useState(e),o=ee(t,2),s=o[0],u=o[1],a=c.useState(null),l=ee(a,2),i=l[0],f=l[1];return c.useEffect(function(){var d=Rr(s||[],e||[],n);(d==null?void 0:d.index)!==void 0&&(r==null||r(d.index),f(e[d.index])),u(e)},[e]),[i]}var en=(typeof navigator>"u"?"undefined":Ge(navigator))==="object"&&/Firefox/i.test(navigator.userAgent);const $n=function(e,n,r,t){var o=c.useRef(!1),s=c.useRef(null);function u(){clearTimeout(s.current),o.current=!0,s.current=setTimeout(function(){o.current=!1},50)}var a=c.useRef({top:e,bottom:n,left:r,right:t});return a.current.top=e,a.current.bottom=n,a.current.left=r,a.current.right=t,function(l,i){var f=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,d=l?i<0&&a.current.left||i>0&&a.current.right:i<0&&a.current.top||i>0&&a.current.bottom;return f&&d?(clearTimeout(s.current),o.current=!1):(!d||o.current)&&u(),!o.current&&d}};function Dr(e,n,r,t,o,s,u){var a=c.useRef(0),l=c.useRef(null),i=c.useRef(null),f=c.useRef(!1),d=$n(n,r,t,o);function h(x,S){if(Re.cancel(l.current),!d(!1,S)){var N=x;if(!N._virtualHandled)N._virtualHandled=!0;else return;a.current+=S,i.current=S,en||N.preventDefault(),l.current=Re(function(){var k=f.current?10:1;u(a.current*k,!1),a.current=0})}}function v(x,S){u(S,!0),en||x.preventDefault()}var y=c.useRef(null),m=c.useRef(null);function g(x){if(e){Re.cancel(m.current),m.current=Re(function(){y.current=null},2);var S=x.deltaX,N=x.deltaY,k=x.shiftKey,$=S,E=N;(y.current==="sx"||!y.current&&k&&N&&!S)&&($=N,E=0,y.current="sx");var O=Math.abs($),p=Math.abs(E);y.current===null&&(y.current=s&&O>p?"x":"y"),y.current==="y"?h(x,E):v(x,$)}}function b(x){e&&(f.current=x.detail===i.current)}return[g,b]}function Pr(e,n,r,t){var o=c.useMemo(function(){return[new Map,[]]},[e,r.id,t]),s=ee(o,2),u=s[0],a=s[1],l=function(f){var d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:f,h=u.get(f),v=u.get(d);if(h===void 0||v===void 0)for(var y=e.length,m=a.length;m<y;m+=1){var g,b=e[m],x=n(b);u.set(x,m);var S=(g=r.get(x))!==null&&g!==void 0?g:t;if(a[m]=(a[m-1]||0)+S,x===f&&(h=m),x===d&&(v=m),h!==void 0&&v!==void 0)break}return{top:a[h-1]||0,bottom:a[v]}};return l}var Mr=function(){function e(){En(this,e),w(this,"maps",void 0),w(this,"id",0),w(this,"diffRecords",new Map),this.maps=Object.create(null)}return Cn(e,[{key:"set",value:function(r,t){this.diffRecords.set(r,this.maps[r]),this.maps[r]=t,this.id+=1}},{key:"get",value:function(r){return this.maps[r]}},{key:"resetRecord",value:function(){this.diffRecords.clear()}},{key:"getRecord",value:function(){return this.diffRecords}}]),e}();function tn(e){var n=parseFloat(e);return isNaN(n)?0:n}function Tr(e,n,r){var t=c.useState(0),o=ee(t,2),s=o[0],u=o[1],a=c.useRef(new Map),l=c.useRef(new Mr),i=c.useRef(0);function f(){i.current+=1}function d(){var v=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;f();var y=function(){var b=!1;a.current.forEach(function(x,S){if(x&&x.offsetParent){var N=x.offsetHeight,k=getComputedStyle(x),$=k.marginTop,E=k.marginBottom,O=tn($),p=tn(E),D=N+O+p;l.current.get(S)!==D&&(l.current.set(S,D),b=!0)}}),b&&u(function(x){return x+1})};if(v)y();else{i.current+=1;var m=i.current;Promise.resolve().then(function(){m===i.current&&y()})}}function h(v,y){var m=e(v),g=a.current.get(m);y?(a.current.set(m,y),d()):a.current.delete(m),!g!=!y&&(y?n==null||n(v):r==null||r(v))}return c.useEffect(function(){return f},[]),[h,d,l.current,s]}var nn=14/15;function jr(e,n,r){var t=c.useRef(!1),o=c.useRef(0),s=c.useRef(0),u=c.useRef(null),a=c.useRef(null),l,i=function(v){if(t.current){var y=Math.ceil(v.touches[0].pageX),m=Math.ceil(v.touches[0].pageY),g=o.current-y,b=s.current-m,x=Math.abs(g)>Math.abs(b);x?o.current=y:s.current=m;var S=r(x,x?g:b,!1,v);S&&v.preventDefault(),clearInterval(a.current),S&&(a.current=setInterval(function(){x?g*=nn:b*=nn;var N=Math.floor(x?g:b);(!r(x,N,!0)||Math.abs(N)<=.1)&&clearInterval(a.current)},16))}},f=function(){t.current=!1,l()},d=function(v){l(),v.touches.length===1&&!t.current&&(t.current=!0,o.current=Math.ceil(v.touches[0].pageX),s.current=Math.ceil(v.touches[0].pageY),u.current=v.target,u.current.addEventListener("touchmove",i,{passive:!1}),u.current.addEventListener("touchend",f,{passive:!0}))};l=function(){u.current&&(u.current.removeEventListener("touchmove",i),u.current.removeEventListener("touchend",f))},je(function(){return e&&n.current.addEventListener("touchstart",d,{passive:!0}),function(){var h;(h=n.current)===null||h===void 0||h.removeEventListener("touchstart",d),l(),clearInterval(a.current)}},[e])}function rn(e){return Math.floor(Math.pow(e,.5))}function kt(e,n){var r="touches"in e?e.touches[0]:e;return r[n?"pageX":"pageY"]-window[n?"scrollX":"scrollY"]}function Ir(e,n,r){c.useEffect(function(){var t=n.current;if(e&&t){var o=!1,s,u,a=function(){Re.cancel(s)},l=function h(){a(),s=Re(function(){r(u),h()})},i=function(v){if(!(v.target.draggable||v.button!==0)){var y=v;y._virtualHandled||(y._virtualHandled=!0,o=!0)}},f=function(){o=!1,a()},d=function(v){if(o){var y=kt(v,!1),m=t.getBoundingClientRect(),g=m.top,b=m.bottom;if(y<=g){var x=g-y;u=-rn(x),l()}else if(y>=b){var S=y-b;u=rn(S),l()}else a()}};return t.addEventListener("mousedown",i),t.ownerDocument.addEventListener("mouseup",f),t.ownerDocument.addEventListener("mousemove",d),function(){t.removeEventListener("mousedown",i),t.ownerDocument.removeEventListener("mouseup",f),t.ownerDocument.removeEventListener("mousemove",d),a()}}},[e])}var Lr=10;function Br(e,n,r,t,o,s,u,a){var l=c.useRef(),i=c.useState(null),f=ee(i,2),d=f[0],h=f[1];return je(function(){if(d&&d.times<Lr){if(!e.current){h(function(re){return Y({},re)});return}s();var v=d.targetAlign,y=d.originAlign,m=d.index,g=d.offset,b=e.current.clientHeight,x=!1,S=v,N=null;if(b){for(var k=v||y,$=0,E=0,O=0,p=Math.min(n.length-1,m),D=0;D<=p;D+=1){var P=o(n[D]);E=$;var H=r.get(P);O=E+(H===void 0?t:H),$=O}for(var A=k==="top"?g:b-g,I=p;I>=0;I-=1){var L=o(n[I]),j=r.get(L);if(j===void 0){x=!0;break}if(A-=j,A<=0)break}switch(k){case"top":N=E-g;break;case"bottom":N=O-b+g;break;default:{var V=e.current.scrollTop,J=V+b;E<V?S="top":O>J&&(S="bottom")}}N!==null&&u(N),N!==d.lastTop&&(x=!0)}x&&h(Y(Y({},d),{},{times:d.times+1,targetAlign:S,lastTop:N}))}},[d,e.current]),function(v){if(v==null){a();return}if(Re.cancel(l.current),typeof v=="number")u(v);else if(v&&Ge(v)==="object"){var y,m=v.align;"index"in v?y=v.index:y=n.findIndex(function(x){return o(x)===v.key});var g=v.offset,b=g===void 0?0:g;h({times:0,index:y,offset:b,originAlign:m})}}}var an=c.forwardRef(function(e,n){var r=e.prefixCls,t=e.rtl,o=e.scrollOffset,s=e.scrollRange,u=e.onStartMove,a=e.onStopMove,l=e.onScroll,i=e.horizontal,f=e.spinSize,d=e.containerSize,h=e.style,v=e.thumbStyle,y=e.showScrollBar,m=c.useState(!1),g=ee(m,2),b=g[0],x=g[1],S=c.useState(null),N=ee(S,2),k=N[0],$=N[1],E=c.useState(null),O=ee(E,2),p=O[0],D=O[1],P=!t,H=c.useRef(),A=c.useRef(),I=c.useState(y),L=ee(I,2),j=L[0],V=L[1],J=c.useRef(),re=function(){y===!0||y===!1||(clearTimeout(J.current),V(!0),J.current=setTimeout(function(){V(!1)},3e3))},U=s-d||0,C=d-f||0,B=c.useMemo(function(){if(o===0||U===0)return 0;var te=o/U;return te*C},[o,U,C]),M=function(Z){Z.stopPropagation(),Z.preventDefault()},z=c.useRef({top:B,dragging:b,pageY:k,startTop:p});z.current={top:B,dragging:b,pageY:k,startTop:p};var he=function(Z){x(!0),$(kt(Z,i)),D(z.current.top),u(),Z.stopPropagation(),Z.preventDefault()};c.useEffect(function(){var te=function(ye){ye.preventDefault()},Z=H.current,ae=A.current;return Z.addEventListener("touchstart",te,{passive:!1}),ae.addEventListener("touchstart",he,{passive:!1}),function(){Z.removeEventListener("touchstart",te),ae.removeEventListener("touchstart",he)}},[]);var le=c.useRef();le.current=U;var $e=c.useRef();$e.current=C,c.useEffect(function(){if(b){var te,Z=function(ye){var Ne=z.current,De=Ne.dragging,Oe=Ne.pageY,Ie=Ne.startTop;Re.cancel(te);var Se=H.current.getBoundingClientRect(),G=d/(i?Se.width:Se.height);if(De){var oe=(kt(ye,i)-Oe)*G,fe=Ie;!P&&i?fe-=oe:fe+=oe;var ke=le.current,Ke=$e.current,Pe=Ke?fe/Ke:0,me=Math.ceil(Pe*ke);me=Math.max(me,0),me=Math.min(me,ke),te=Re(function(){l(me,i)})}},ae=function(){x(!1),a()};return window.addEventListener("mousemove",Z,{passive:!0}),window.addEventListener("touchmove",Z,{passive:!0}),window.addEventListener("mouseup",ae,{passive:!0}),window.addEventListener("touchend",ae,{passive:!0}),function(){window.removeEventListener("mousemove",Z),window.removeEventListener("touchmove",Z),window.removeEventListener("mouseup",ae),window.removeEventListener("touchend",ae),Re.cancel(te)}}},[b]),c.useEffect(function(){return re(),function(){clearTimeout(J.current)}},[o]),c.useImperativeHandle(n,function(){return{delayHidden:re}});var ce="".concat(r,"-scrollbar"),de={position:"absolute",visibility:j?null:"hidden"},Ee={position:"absolute",borderRadius:99,background:"var(--rc-virtual-list-scrollbar-bg, rgba(0, 0, 0, 0.5))",cursor:"pointer",userSelect:"none"};return i?(Object.assign(de,{height:8,left:0,right:0,bottom:0}),Object.assign(Ee,w({height:"100%",width:f},P?"left":"right",B))):(Object.assign(de,w({width:8,top:0,bottom:0},P?"right":"left",0)),Object.assign(Ee,{width:"100%",height:f,top:B})),c.createElement("div",{ref:H,className:ne(ce,w(w(w({},"".concat(ce,"-horizontal"),i),"".concat(ce,"-vertical"),!i),"".concat(ce,"-visible"),j)),style:Y(Y({},de),h),onMouseDown:M,onMouseMove:re},c.createElement("div",{ref:A,className:ne("".concat(ce,"-thumb"),w({},"".concat(ce,"-thumb-moving"),b)),style:Y(Y({},Ee),v),onMouseDown:he}))}),Hr=20;function on(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=e/n*e;return isNaN(r)&&(r=0),r=Math.max(r,Hr),Math.floor(r)}var zr=["prefixCls","className","height","itemHeight","fullHeight","style","data","children","itemKey","virtual","direction","scrollWidth","component","onScroll","onVirtualScroll","onVisibleChange","innerProps","extraRender","styles","showScrollBar"],Ar=[],Fr={overflowY:"auto",overflowAnchor:"none"};function Wr(e,n){var r=e.prefixCls,t=r===void 0?"rc-virtual-list":r,o=e.className,s=e.height,u=e.itemHeight,a=e.fullHeight,l=a===void 0?!0:a,i=e.style,f=e.data,d=e.children,h=e.itemKey,v=e.virtual,y=e.direction,m=e.scrollWidth,g=e.component,b=g===void 0?"div":g,x=e.onScroll,S=e.onVirtualScroll,N=e.onVisibleChange,k=e.innerProps,$=e.extraRender,E=e.styles,O=e.showScrollBar,p=O===void 0?"optional":O,D=tt(e,zr),P=c.useCallback(function(T){return typeof h=="function"?h(T):T==null?void 0:T[h]},[h]),H=Tr(P,null,null),A=ee(H,4),I=A[0],L=A[1],j=A[2],V=A[3],J=!!(v!==!1&&s&&u),re=c.useMemo(function(){return Object.values(j.maps).reduce(function(T,R){return T+R},0)},[j.id,j.maps]),U=J&&f&&(Math.max(u*f.length,re)>s||!!m),C=y==="rtl",B=ne(t,w({},"".concat(t,"-rtl"),C),o),M=f||Ar,z=c.useRef(),he=c.useRef(),le=c.useRef(),$e=c.useState(0),ce=ee($e,2),de=ce[0],Ee=ce[1],te=c.useState(0),Z=ee(te,2),ae=Z[0],pe=Z[1],ye=c.useState(!1),Ne=ee(ye,2),De=Ne[0],Oe=Ne[1],Ie=function(){Oe(!0)},Se=function(){Oe(!1)},G={getKey:P};function oe(T){Ee(function(R){var W;typeof T=="function"?W=T(R):W=T;var Q=qn(W);return z.current.scrollTop=Q,Q})}var fe=c.useRef({start:0,end:M.length}),ke=c.useRef(),Ke=_r(M,P),Pe=ee(Ke,1),me=Pe[0];ke.current=me;var Ce=c.useMemo(function(){if(!J)return{scrollHeight:void 0,start:0,end:M.length-1,offset:void 0};if(!U){var T;return{scrollHeight:((T=he.current)===null||T===void 0?void 0:T.offsetHeight)||0,start:0,end:M.length-1,offset:void 0}}for(var R=0,W,Q,ue,it=M.length,ze=0;ze<it;ze+=1){var gt=M[ze],Qn=P(gt),Ut=j.get(Qn),pt=R+(Ut===void 0?u:Ut);pt>=de&&W===void 0&&(W=ze,Q=R),pt>de+s&&ue===void 0&&(ue=ze),R=pt}return W===void 0&&(W=0,Q=0,ue=Math.ceil(s/u)),ue===void 0&&(ue=M.length-1),ue=Math.min(ue+1,M.length-1),{scrollHeight:R,start:W,end:ue,offset:Q}},[U,J,de,M,V,s]),ge=Ce.scrollHeight,Me=Ce.start,X=Ce.end,_=Ce.offset;fe.current.start=Me,fe.current.end=X,c.useLayoutEffect(function(){var T=j.getRecord();if(T.size===1){var R=Array.from(T.keys())[0],W=T.get(R),Q=M[Me];if(Q&&W===void 0){var ue=P(Q);if(ue===R){var it=j.get(R),ze=it-u;oe(function(gt){return gt+ze})}}}j.resetRecord()},[ge]);var we=c.useState({width:0,height:s}),qe=ee(we,2),be=qe[0],Ve=qe[1],An=function(R){Ve({width:R.offsetWidth,height:R.offsetHeight})},Lt=c.useRef(),Bt=c.useRef(),Fn=c.useMemo(function(){return on(be.width,m)},[be.width,m]),Wn=c.useMemo(function(){return on(be.height,ge)},[be.height,ge]),ct=ge-s,dt=c.useRef(ct);dt.current=ct;function qn(T){var R=T;return Number.isNaN(dt.current)||(R=Math.min(R,dt.current)),R=Math.max(R,0),R}var rt=de<=0,at=de>=ct,Ht=ae<=0,zt=ae>=m,Vn=$n(rt,at,Ht,zt),ut=function(){return{x:C?-ae:ae,y:de}},ft=c.useRef(ut()),ot=Gt(function(T){if(S){var R=Y(Y({},ut()),T);(ft.current.x!==R.x||ft.current.y!==R.y)&&(S(R),ft.current=R)}});function At(T,R){var W=T;R?(Yt.flushSync(function(){pe(W)}),ot()):oe(W)}function Xn(T){var R=T.currentTarget.scrollTop;R!==de&&oe(R),x==null||x(T),ot()}var vt=function(R){var W=R,Q=m?m-be.width:0;return W=Math.max(W,0),W=Math.min(W,Q),W},Un=Gt(function(T,R){R?(Yt.flushSync(function(){pe(function(W){var Q=W+(C?-T:T);return vt(Q)})}),ot()):oe(function(W){var Q=W+T;return Q})}),Gn=Dr(J,rt,at,Ht,zt,!!m,Un),Ft=ee(Gn,2),ht=Ft[0],Wt=Ft[1];jr(J,z,function(T,R,W,Q){var ue=Q;return Vn(T,R,W)?!1:!ue||!ue._virtualHandled?(ue&&(ue._virtualHandled=!0),ht({preventDefault:function(){},deltaX:T?R:0,deltaY:T?0:R}),!0):!1}),Ir(U,z,function(T){oe(function(R){return R+T})}),je(function(){function T(W){var Q=rt&&W.detail<0,ue=at&&W.detail>0;J&&!Q&&!ue&&W.preventDefault()}var R=z.current;return R.addEventListener("wheel",ht,{passive:!1}),R.addEventListener("DOMMouseScroll",Wt,{passive:!0}),R.addEventListener("MozMousePixelScroll",T,{passive:!1}),function(){R.removeEventListener("wheel",ht),R.removeEventListener("DOMMouseScroll",Wt),R.removeEventListener("MozMousePixelScroll",T)}},[J,rt,at]),je(function(){if(m){var T=vt(ae);pe(T),ot({x:T})}},[be.width,m]);var qt=function(){var R,W;(R=Lt.current)===null||R===void 0||R.delayHidden(),(W=Bt.current)===null||W===void 0||W.delayHidden()},Vt=Br(z,M,j,u,P,function(){return L(!0)},oe,qt);c.useImperativeHandle(n,function(){return{nativeElement:le.current,getScrollInfo:ut,scrollTo:function(R){function W(Q){return Q&&Ge(Q)==="object"&&("left"in Q||"top"in Q)}W(R)?(R.left!==void 0&&pe(vt(R.left)),Vt(R.top)):Vt(R)}}}),je(function(){if(N){var T=M.slice(Me,X+1);N(T,M)}},[Me,X,M]);var Yn=Pr(M,P,j,u),Jn=$==null?void 0:$({start:Me,end:X,virtual:U,offsetX:ae,offsetY:_,rtl:C,getSize:Yn}),Zn=Or(M,Me,X,m,ae,I,d,G),Ye=null;s&&(Ye=Y(w({},l?"height":"maxHeight",s),Fr),J&&(Ye.overflowY="hidden",m&&(Ye.overflowX="hidden"),De&&(Ye.pointerEvents="none")));var Xt={};return C&&(Xt.dir="rtl"),c.createElement("div",ve({ref:le,style:Y(Y({},i),{},{position:"relative"}),className:B},Xt,D),c.createElement(Sn,{onResize:An},c.createElement(b,{className:"".concat(t,"-holder"),style:Ye,ref:z,onScroll:Xn,onMouseEnter:qt},c.createElement(kn,{prefixCls:t,height:ge,offsetX:ae,offsetY:_,scrollWidth:m,onInnerResize:L,ref:he,innerProps:k,rtl:C,extra:Jn},Zn))),U&&ge>s&&c.createElement(an,{ref:Lt,prefixCls:t,scrollOffset:de,scrollRange:ge,rtl:C,onScroll:At,onStartMove:Ie,onStopMove:Se,spinSize:Wn,containerSize:be.height,style:E==null?void 0:E.verticalScrollBar,thumbStyle:E==null?void 0:E.verticalScrollBarThumb,showScrollBar:p}),U&&m>be.width&&c.createElement(an,{ref:Bt,prefixCls:t,scrollOffset:ae,scrollRange:m,rtl:C,onScroll:At,onStartMove:Ie,onStopMove:Se,spinSize:Fn,containerSize:be.width,horizontal:!0,style:E==null?void 0:E.horizontalScrollBar,thumbStyle:E==null?void 0:E.horizontalScrollBarThumb,showScrollBar:p}))}var On=c.forwardRef(Wr);On.displayName="List";const lt=e=>e?typeof e=="function"?e():e:null,qr=e=>{const{componentCls:n,popoverColor:r,titleMinWidth:t,fontWeightStrong:o,innerPadding:s,boxShadowSecondary:u,colorTextHeading:a,borderRadiusLG:l,zIndexPopup:i,titleMarginBottom:f,colorBgElevated:d,popoverBg:h,titleBorderBottom:v,innerContentPadding:y,titlePadding:m}=e;return[{[n]:Object.assign(Object.assign({},Je(e)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:i,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:["var(--valid-offset-x, 50%)","var(--arrow-y, 50%)"].join(" "),"--antd-arrow-background-color":d,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${n}-content`]:{position:"relative"},[`${n}-inner`]:{backgroundColor:h,backgroundClip:"padding-box",borderRadius:l,boxShadow:u,padding:s},[`${n}-title`]:{minWidth:t,marginBottom:f,color:a,fontWeight:o,borderBottom:v,padding:m},[`${n}-inner-content`]:{color:r,padding:y}})},tr(e,"var(--antd-arrow-background-color)"),{[`${n}-pure`]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow,display:"inline-block",[`${n}-content`]:{display:"inline-block"}}}]},Vr=e=>{const{componentCls:n}=e;return{[n]:nr.map(r=>{const t=e[`${r}6`];return{[`&${n}-${r}`]:{"--antd-arrow-background-color":t,[`${n}-inner`]:{backgroundColor:t},[`${n}-arrow`]:{background:"transparent"}}}})}},Xr=e=>{const{lineWidth:n,controlHeight:r,fontHeight:t,padding:o,wireframe:s,zIndexPopupBase:u,borderRadiusLG:a,marginXS:l,lineType:i,colorSplit:f,paddingSM:d}=e,h=r-t,v=h/2,y=h/2-n,m=o;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:u+30},rr(e)),ar({contentRadius:a,limitVerticalRadius:!0})),{innerPadding:s?0:12,titleMarginBottom:s?0:l,titlePadding:s?`${v}px ${m}px ${y}px`:0,titleBorderBottom:s?`${n}px ${i} ${f}`:"none",innerContentPadding:s?`${d}px ${m}px`:0})},Rn=Rt("Popover",e=>{const{colorBgElevated:n,colorText:r}=e,t=_t(e,{popoverBg:n,popoverColor:r});return[qr(t),Vr(t),er(t,"zoom-big")]},Xr,{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]});var Ur=globalThis&&globalThis.__rest||function(e,n){var r={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(r[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,t=Object.getOwnPropertySymbols(e);o<t.length;o++)n.indexOf(t[o])<0&&Object.prototype.propertyIsEnumerable.call(e,t[o])&&(r[t[o]]=e[t[o]]);return r};const _n=({title:e,content:n,prefixCls:r})=>!e&&!n?null:c.createElement(c.Fragment,null,e&&c.createElement("div",{className:`${r}-title`},e),n&&c.createElement("div",{className:`${r}-inner-content`},n)),Gr=e=>{const{hashId:n,prefixCls:r,className:t,style:o,placement:s="top",title:u,content:a,children:l}=e,i=lt(u),f=lt(a),d=ne(n,r,`${r}-pure`,`${r}-placement-${s}`,t);return c.createElement("div",{className:d,style:o},c.createElement("div",{className:`${r}-arrow`}),c.createElement(or,Object.assign({},e,{className:n,prefixCls:r}),l||c.createElement(_n,{prefixCls:r,title:i,content:f})))},Yr=e=>{const{prefixCls:n,className:r}=e,t=Ur(e,["prefixCls","className"]),{getPrefixCls:o}=c.useContext(Dt),s=o("popover",n),[u,a,l]=Rn(s);return u(c.createElement(Gr,Object.assign({},t,{prefixCls:s,hashId:a,className:ne(r,l)})))},Jr=Yr;var Zr=globalThis&&globalThis.__rest||function(e,n){var r={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(r[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,t=Object.getOwnPropertySymbols(e);o<t.length;o++)n.indexOf(t[o])<0&&Object.prototype.propertyIsEnumerable.call(e,t[o])&&(r[t[o]]=e[t[o]]);return r};const Qr=c.forwardRef((e,n)=>{var r,t;const{prefixCls:o,title:s,content:u,overlayClassName:a,placement:l="top",trigger:i="hover",children:f,mouseEnterDelay:d=.1,mouseLeaveDelay:h=.1,onOpenChange:v,overlayStyle:y={},styles:m,classNames:g}=e,b=Zr(e,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle","styles","classNames"]),{getPrefixCls:x,className:S,style:N,classNames:k,styles:$}=ir("popover"),E=x("popover",o),[O,p,D]=Rn(E),P=x(),H=ne(a,p,D,S,k.root,g==null?void 0:g.root),A=ne(k.body,g==null?void 0:g.body),[I,L]=sr(!1,{value:(r=e.open)!==null&&r!==void 0?r:e.visible,defaultValue:(t=e.defaultOpen)!==null&&t!==void 0?t:e.defaultVisible}),j=(C,B)=>{L(C,!0),v==null||v(C,B)},V=C=>{C.keyCode===Ae.ESC&&j(!1,C)},J=C=>{j(C)},re=lt(s),U=lt(u);return O(c.createElement(lr,Object.assign({placement:l,trigger:i,mouseEnterDelay:d,mouseLeaveDelay:h},b,{prefixCls:E,classNames:{root:H,body:A},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},$.root),N),y),m==null?void 0:m.root),body:Object.assign(Object.assign({},$.body),m==null?void 0:m.body)},ref:n,open:I,onOpenChange:J,overlay:re||U?c.createElement(_n,{prefixCls:E,title:re,content:U}):null,transitionName:cr(P,"zoom-big",b.transitionName),"data-popover-inject":!0}),wt(f,{onKeyDown:C=>{var B,M;c.isValidElement(f)&&((M=f==null?void 0:(B=f.props).onKeyDown)===null||M===void 0||M.call(B,C)),V(C)}})))}),Dn=Qr;Dn._InternalPanelDoNotUseOrYouWillBeFired=Jr;const ea=Dn;function xe(e,n){return e[n]}var ta=["children"];function Pn(e,n){return"".concat(e,"-").concat(n)}function na(e){return e&&e.type&&e.type.isTreeNode}function nt(e,n){return e??n}function Ue(e){var n=e||{},r=n.title,t=n._title,o=n.key,s=n.children,u=r||"title";return{title:u,_title:t||[u],key:o||"key",children:s||"children"}}function Mn(e){function n(r){var t=dr(r);return t.map(function(o){if(!na(o))return He(!o,"Tree/TreeNode can only accept TreeNode as children."),null;var s=o.key,u=o.props,a=u.children,l=tt(u,ta),i=Y({key:s},l),f=n(a);return f.length&&(i.children=f),i}).filter(function(o){return o})}return n(e)}function xt(e,n,r){var t=Ue(r),o=t._title,s=t.key,u=t.children,a=new Set(n===!0?[]:n),l=[];function i(f){var d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return f.map(function(h,v){for(var y=Pn(d?d.pos:"0",v),m=nt(h[s],y),g,b=0;b<o.length;b+=1){var x=o[b];if(h[x]!==void 0){g=h[x];break}}var S=Object.assign(ur(h,[].concat(_e(o),[s,u])),{title:g,key:m,parent:d,pos:y,children:null,data:h,isStart:[].concat(_e(d?d.isStart:[]),[v===0]),isEnd:[].concat(_e(d?d.isEnd:[]),[v===f.length-1])});return l.push(S),n===!0||a.has(m)?S.children=i(h[u]||[],S):S.children=[],S})}return i(e),l}function ra(e,n,r){var t={};Ge(r)==="object"?t=r:t={externalGetKey:r},t=t||{};var o=t,s=o.childrenPropName,u=o.externalGetKey,a=o.fieldNames,l=Ue(a),i=l.key,f=l.children,d=s||f,h;u?typeof u=="string"?h=function(m){return m[u]}:typeof u=="function"&&(h=function(m){return u(m)}):h=function(m,g){return nt(m[i],g)};function v(y,m,g,b){var x=y?y[d]:e,S=y?Pn(g.pos,m):"0",N=y?[].concat(_e(b),[y]):[];if(y){var k=h(y,S),$={node:y,index:m,pos:S,key:k,parentPos:g.node?g.pos:null,level:g.level+1,nodes:N};n($)}x&&x.forEach(function(E,O){v(E,O,{node:y,pos:S,level:g?g.level+1:-1},N)})}v(null)}function Tn(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=n.initWrapper,t=n.processEntity,o=n.onProcessFinished,s=n.externalGetKey,u=n.childrenPropName,a=n.fieldNames,l=arguments.length>2?arguments[2]:void 0,i=s||l,f={},d={},h={posEntities:f,keyEntities:d};return r&&(h=r(h)||h),ra(e,function(v){var y=v.node,m=v.index,g=v.pos,b=v.key,x=v.parentPos,S=v.level,N=v.nodes,k={node:y,nodes:N,index:m,key:b,pos:g,level:S},$=nt(b,g);f[g]=k,d[$]=k,k.parent=f[x],k.parent&&(k.parent.children=k.parent.children||[],k.parent.children.push(k)),t&&t(k,h)},{externalGetKey:i,childrenPropName:u,fieldNames:a}),o&&o(h),h}function Ze(e,n){var r=n.expandedKeys,t=n.selectedKeys,o=n.loadedKeys,s=n.loadingKeys,u=n.checkedKeys,a=n.halfCheckedKeys,l=n.dragOverNodeKey,i=n.dropPosition,f=n.keyEntities,d=xe(f,e),h={eventKey:e,expanded:r.indexOf(e)!==-1,selected:t.indexOf(e)!==-1,loaded:o.indexOf(e)!==-1,loading:s.indexOf(e)!==-1,checked:u.indexOf(e)!==-1,halfChecked:a.indexOf(e)!==-1,pos:String(d?d.pos:""),dragOver:l===e&&i===0,dragOverGapTop:l===e&&i===-1,dragOverGapBottom:l===e&&i===1};return h}function se(e){var n=e.data,r=e.expanded,t=e.selected,o=e.checked,s=e.loaded,u=e.loading,a=e.halfChecked,l=e.dragOver,i=e.dragOverGapTop,f=e.dragOverGapBottom,d=e.pos,h=e.active,v=e.eventKey,y=Y(Y({},n),{},{expanded:r,selected:t,checked:o,loaded:s,loading:u,halfChecked:a,dragOver:l,dragOverGapTop:i,dragOverGapBottom:f,pos:d,active:h,key:v});return"props"in y||Object.defineProperty(y,"props",{get:function(){return He(!1,"Second param return from event is node data instead of TreeNode instance. Please read value directly instead of reading from `props`."),e}}),y}function jn(e,n){var r=new Set;return e.forEach(function(t){n.has(t)||r.add(t)}),r}function aa(e){var n=e||{},r=n.disabled,t=n.disableCheckbox,o=n.checkable;return!!(r||t)||o===!1}function oa(e,n,r,t){for(var o=new Set(e),s=new Set,u=0;u<=r;u+=1){var a=n.get(u)||new Set;a.forEach(function(d){var h=d.key,v=d.node,y=d.children,m=y===void 0?[]:y;o.has(h)&&!t(v)&&m.filter(function(g){return!t(g.node)}).forEach(function(g){o.add(g.key)})})}for(var l=new Set,i=r;i>=0;i-=1){var f=n.get(i)||new Set;f.forEach(function(d){var h=d.parent,v=d.node;if(!(t(v)||!d.parent||l.has(d.parent.key))){if(t(d.parent.node)){l.add(h.key);return}var y=!0,m=!1;(h.children||[]).filter(function(g){return!t(g.node)}).forEach(function(g){var b=g.key,x=o.has(b);y&&!x&&(y=!1),!m&&(x||s.has(b))&&(m=!0)}),y&&o.add(h.key),m&&s.add(h.key),l.add(h.key)}})}return{checkedKeys:Array.from(o),halfCheckedKeys:Array.from(jn(s,o))}}function ia(e,n,r,t,o){for(var s=new Set(e),u=new Set(n),a=0;a<=t;a+=1){var l=r.get(a)||new Set;l.forEach(function(h){var v=h.key,y=h.node,m=h.children,g=m===void 0?[]:m;!s.has(v)&&!u.has(v)&&!o(y)&&g.filter(function(b){return!o(b.node)}).forEach(function(b){s.delete(b.key)})})}u=new Set;for(var i=new Set,f=t;f>=0;f-=1){var d=r.get(f)||new Set;d.forEach(function(h){var v=h.parent,y=h.node;if(!(o(y)||!h.parent||i.has(h.parent.key))){if(o(h.parent.node)){i.add(v.key);return}var m=!0,g=!1;(v.children||[]).filter(function(b){return!o(b.node)}).forEach(function(b){var x=b.key,S=s.has(x);m&&!S&&(m=!1),!g&&(S||u.has(x))&&(g=!0)}),m||s.delete(v.key),g&&u.add(v.key),i.add(v.key)}})}return{checkedKeys:Array.from(s),halfCheckedKeys:Array.from(jn(u,s))}}function St(e,n,r,t){var o=[],s;t?s=t:s=aa;var u=new Set(e.filter(function(f){var d=!!xe(r,f);return d||o.push(f),d})),a=new Map,l=0;Object.keys(r).forEach(function(f){var d=r[f],h=d.level,v=a.get(h);v||(v=new Set,a.set(h,v)),v.add(d),l=Math.max(l,h)}),He(!o.length,"Tree missing follow keys: ".concat(o.slice(0,100).map(function(f){return"'".concat(f,"'")}).join(", ")));var i;return n===!0?i=oa(u,a,l,s):i=ia(u,n.halfCheckedKeys,a,l,s),i}const sa=e=>{const{checkboxCls:n}=e,r=`${n}-wrapper`;return[{[`${n}-group`]:Object.assign(Object.assign({},Je(e)),{display:"inline-flex",flexWrap:"wrap",columnGap:e.marginXS,[`> ${e.antCls}-row`]:{flex:1}}),[r]:Object.assign(Object.assign({},Je(e)),{display:"inline-flex",alignItems:"baseline",cursor:"pointer","&:after":{display:"inline-block",width:0,overflow:"hidden",content:"'\\a0'"},[`& + ${r}`]:{marginInlineStart:0},[`&${r}-in-form-item`]:{'input[type="checkbox"]':{width:14,height:14}}}),[n]:Object.assign(Object.assign({},Je(e)),{position:"relative",whiteSpace:"nowrap",lineHeight:1,cursor:"pointer",borderRadius:e.borderRadiusSM,alignSelf:"center",[`${n}-input`]:{position:"absolute",inset:0,zIndex:1,cursor:"pointer",opacity:0,margin:0,[`&:focus-visible + ${n}-inner`]:Nn(e)},[`${n}-inner`]:{boxSizing:"border-box",display:"block",width:e.checkboxSize,height:e.checkboxSize,direction:"ltr",backgroundColor:e.colorBgContainer,border:`${Qe(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,borderCollapse:"separate",transition:`all ${e.motionDurationSlow}`,"&:after":{boxSizing:"border-box",position:"absolute",top:"50%",insetInlineStart:"25%",display:"table",width:e.calc(e.checkboxSize).div(14).mul(5).equal(),height:e.calc(e.checkboxSize).div(14).mul(8).equal(),border:`${Qe(e.lineWidthBold)} solid ${e.colorWhite}`,borderTop:0,borderInlineStart:0,transform:"rotate(45deg) scale(0) translate(-50%,-50%)",opacity:0,content:'""',transition:`all ${e.motionDurationFast} ${e.motionEaseInBack}, opacity ${e.motionDurationFast}`}},"& + span":{paddingInlineStart:e.paddingXS,paddingInlineEnd:e.paddingXS}})},{[`
        ${r}:not(${r}-disabled),
        ${n}:not(${n}-disabled)
      `]:{[`&:hover ${n}-inner`]:{borderColor:e.colorPrimary}},[`${r}:not(${r}-disabled)`]:{[`&:hover ${n}-checked:not(${n}-disabled) ${n}-inner`]:{backgroundColor:e.colorPrimaryHover,borderColor:"transparent"},[`&:hover ${n}-checked:not(${n}-disabled):after`]:{borderColor:e.colorPrimaryHover}}},{[`${n}-checked`]:{[`${n}-inner`]:{backgroundColor:e.colorPrimary,borderColor:e.colorPrimary,"&:after":{opacity:1,transform:"rotate(45deg) scale(1) translate(-50%,-50%)",transition:`all ${e.motionDurationMid} ${e.motionEaseOutBack} ${e.motionDurationFast}`}}},[`
        ${r}-checked:not(${r}-disabled),
        ${n}-checked:not(${n}-disabled)
      `]:{[`&:hover ${n}-inner`]:{backgroundColor:e.colorPrimaryHover,borderColor:"transparent"}}},{[n]:{"&-indeterminate":{"&":{[`${n}-inner`]:{backgroundColor:`${e.colorBgContainer}`,borderColor:`${e.colorBorder}`,"&:after":{top:"50%",insetInlineStart:"50%",width:e.calc(e.fontSizeLG).div(2).equal(),height:e.calc(e.fontSizeLG).div(2).equal(),backgroundColor:e.colorPrimary,border:0,transform:"translate(-50%, -50%) scale(1)",opacity:1,content:'""'}},[`&:hover ${n}-inner`]:{backgroundColor:`${e.colorBgContainer}`,borderColor:`${e.colorPrimary}`}}}}},{[`${r}-disabled`]:{cursor:"not-allowed"},[`${n}-disabled`]:{[`&, ${n}-input`]:{cursor:"not-allowed",pointerEvents:"none"},[`${n}-inner`]:{background:e.colorBgContainerDisabled,borderColor:e.colorBorder,"&:after":{borderColor:e.colorTextDisabled}},"&:after":{display:"none"},"& + span":{color:e.colorTextDisabled},[`&${n}-indeterminate ${n}-inner::after`]:{background:e.colorTextDisabled}}}]};function In(e,n){const r=_t(n,{checkboxCls:`.${e}`,checkboxSize:n.controlInteractiveSize});return sa(r)}Rt("Checkbox",(e,{prefixCls:n})=>[In(n,e)]);var Pt=c.createContext(null),la=c.createContext({}),ca=function(n){for(var r=n.prefixCls,t=n.level,o=n.isStart,s=n.isEnd,u="".concat(r,"-indent-unit"),a=[],l=0;l<t;l+=1)a.push(c.createElement("span",{key:l,className:ne(u,w(w({},"".concat(u,"-start"),o[l]),"".concat(u,"-end"),s[l]))}));return c.createElement("span",{"aria-hidden":"true",className:"".concat(r,"-indent")},a)};const da=c.memo(ca);var ua=["eventKey","className","style","dragOver","dragOverGapTop","dragOverGapBottom","isLeaf","isStart","isEnd","expanded","selected","checked","halfChecked","loading","domRef","active","data","onMouseMove","selectable"],sn="open",ln="close",fa="---",et=function(n){var r,t,o,s=n.eventKey,u=n.className,a=n.style,l=n.dragOver,i=n.dragOverGapTop,f=n.dragOverGapBottom,d=n.isLeaf,h=n.isStart,v=n.isEnd,y=n.expanded,m=n.selected,g=n.checked,b=n.halfChecked,x=n.loading,S=n.domRef,N=n.active,k=n.data,$=n.onMouseMove,E=n.selectable,O=tt(n,ua),p=q.useContext(Pt),D=q.useContext(la),P=q.useRef(null),H=q.useState(!1),A=ee(H,2),I=A[0],L=A[1],j=!!(p.disabled||n.disabled||(r=D.nodeDisabled)!==null&&r!==void 0&&r.call(D,k)),V=q.useMemo(function(){return!p.checkable||n.checkable===!1?!1:p.checkable},[p.checkable,n.checkable]),J=function(_){j||p.onNodeSelect(_,se(n))},re=function(_){j||!V||n.disableCheckbox||p.onNodeCheck(_,se(n),!g)},U=q.useMemo(function(){return typeof E=="boolean"?E:p.selectable},[E,p.selectable]),C=function(_){p.onNodeClick(_,se(n)),U?J(_):re(_)},B=function(_){p.onNodeDoubleClick(_,se(n))},M=function(_){p.onNodeMouseEnter(_,se(n))},z=function(_){p.onNodeMouseLeave(_,se(n))},he=function(_){p.onNodeContextMenu(_,se(n))},le=q.useMemo(function(){return!!(p.draggable&&(!p.draggable.nodeDraggable||p.draggable.nodeDraggable(k)))},[p.draggable,k]),$e=function(_){_.stopPropagation(),L(!0),p.onNodeDragStart(_,n);try{_.dataTransfer.setData("text/plain","")}catch{}},ce=function(_){_.preventDefault(),_.stopPropagation(),p.onNodeDragEnter(_,n)},de=function(_){_.preventDefault(),_.stopPropagation(),p.onNodeDragOver(_,n)},Ee=function(_){_.stopPropagation(),p.onNodeDragLeave(_,n)},te=function(_){_.stopPropagation(),L(!1),p.onNodeDragEnd(_,n)},Z=function(_){_.preventDefault(),_.stopPropagation(),L(!1),p.onNodeDrop(_,n)},ae=function(_){x||p.onNodeExpand(_,se(n))},pe=q.useMemo(function(){var X=xe(p.keyEntities,s)||{},_=X.children;return!!(_||[]).length},[p.keyEntities,s]),ye=q.useMemo(function(){return d===!1?!1:d||!p.loadData&&!pe||p.loadData&&n.loaded&&!pe},[d,p.loadData,pe,n.loaded]);q.useEffect(function(){x||typeof p.loadData=="function"&&y&&!ye&&!n.loaded&&p.onNodeLoad(se(n))},[x,p.loadData,p.onNodeLoad,y,ye,n]);var Ne=q.useMemo(function(){var X;return(X=p.draggable)!==null&&X!==void 0&&X.icon?q.createElement("span",{className:"".concat(p.prefixCls,"-draggable-icon")},p.draggable.icon):null},[p.draggable]),De=function(_){var we=n.switcherIcon||p.switcherIcon;return typeof we=="function"?we(Y(Y({},n),{},{isLeaf:_})):we},Oe=function(){if(ye){var _=De(!0);return _!==!1?q.createElement("span",{className:ne("".concat(p.prefixCls,"-switcher"),"".concat(p.prefixCls,"-switcher-noop"))},_):null}var we=De(!1);return we!==!1?q.createElement("span",{onClick:ae,className:ne("".concat(p.prefixCls,"-switcher"),"".concat(p.prefixCls,"-switcher_").concat(y?sn:ln))},we):null},Ie=q.useMemo(function(){if(!V)return null;var X=typeof V!="boolean"?V:null;return q.createElement("span",{className:ne("".concat(p.prefixCls,"-checkbox"),w(w(w({},"".concat(p.prefixCls,"-checkbox-checked"),g),"".concat(p.prefixCls,"-checkbox-indeterminate"),!g&&b),"".concat(p.prefixCls,"-checkbox-disabled"),j||n.disableCheckbox)),onClick:re,role:"checkbox","aria-checked":b?"mixed":g,"aria-disabled":j||n.disableCheckbox,"aria-label":"Select ".concat(typeof n.title=="string"?n.title:"tree node")},X)},[V,g,b,j,n.disableCheckbox,n.title]),Se=q.useMemo(function(){return ye?null:y?sn:ln},[ye,y]),G=q.useMemo(function(){return q.createElement("span",{className:ne("".concat(p.prefixCls,"-iconEle"),"".concat(p.prefixCls,"-icon__").concat(Se||"docu"),w({},"".concat(p.prefixCls,"-icon_loading"),x))})},[p.prefixCls,Se,x]),oe=q.useMemo(function(){var X=!!p.draggable,_=!n.disabled&&X&&p.dragOverNodeKey===s;return _?p.dropIndicatorRender({dropPosition:p.dropPosition,dropLevelOffset:p.dropLevelOffset,indent:p.indent,prefixCls:p.prefixCls,direction:p.direction}):null},[p.dropPosition,p.dropLevelOffset,p.indent,p.prefixCls,p.direction,p.draggable,p.dragOverNodeKey,p.dropIndicatorRender]),fe=q.useMemo(function(){var X=n.title,_=X===void 0?fa:X,we="".concat(p.prefixCls,"-node-content-wrapper"),qe;if(p.showIcon){var be=n.icon||p.icon;qe=be?q.createElement("span",{className:ne("".concat(p.prefixCls,"-iconEle"),"".concat(p.prefixCls,"-icon__customize"))},typeof be=="function"?be(n):be):G}else p.loadData&&x&&(qe=G);var Ve;return typeof _=="function"?Ve=_(k):p.titleRender?Ve=p.titleRender(k):Ve=_,q.createElement("span",{ref:P,title:typeof _=="string"?_:"",className:ne(we,"".concat(we,"-").concat(Se||"normal"),w({},"".concat(p.prefixCls,"-node-selected"),!j&&(m||I))),onMouseEnter:M,onMouseLeave:z,onContextMenu:he,onClick:C,onDoubleClick:B},qe,q.createElement("span",{className:"".concat(p.prefixCls,"-title")},Ve),oe)},[p.prefixCls,p.showIcon,n,p.icon,G,p.titleRender,k,Se,M,z,he,C,B]),ke=Kn(O,{aria:!0,data:!0}),Ke=xe(p.keyEntities,s)||{},Pe=Ke.level,me=v[v.length-1],Ce=!j&&le,ge=p.draggingNodeKey===s,Me=E!==void 0?{"aria-selected":!!E}:void 0;return q.createElement("div",ve({ref:S,role:"treeitem","aria-expanded":d?void 0:y,className:ne(u,"".concat(p.prefixCls,"-treenode"),(o={},w(w(w(w(w(w(w(w(w(w(o,"".concat(p.prefixCls,"-treenode-disabled"),j),"".concat(p.prefixCls,"-treenode-switcher-").concat(y?"open":"close"),!d),"".concat(p.prefixCls,"-treenode-checkbox-checked"),g),"".concat(p.prefixCls,"-treenode-checkbox-indeterminate"),b),"".concat(p.prefixCls,"-treenode-selected"),m),"".concat(p.prefixCls,"-treenode-loading"),x),"".concat(p.prefixCls,"-treenode-active"),N),"".concat(p.prefixCls,"-treenode-leaf-last"),me),"".concat(p.prefixCls,"-treenode-draggable"),le),"dragging",ge),w(w(w(w(w(w(w(o,"drop-target",p.dropTargetKey===s),"drop-container",p.dropContainerKey===s),"drag-over",!j&&l),"drag-over-gap-top",!j&&i),"drag-over-gap-bottom",!j&&f),"filter-node",(t=p.filterTreeNode)===null||t===void 0?void 0:t.call(p,se(n))),"".concat(p.prefixCls,"-treenode-leaf"),ye))),style:a,draggable:Ce,onDragStart:Ce?$e:void 0,onDragEnter:le?ce:void 0,onDragOver:le?de:void 0,onDragLeave:le?Ee:void 0,onDrop:le?Z:void 0,onDragEnd:le?te:void 0,onMouseMove:$},Me,ke),q.createElement(da,{prefixCls:p.prefixCls,level:Pe,isStart:h,isEnd:v}),Ne,Oe(),Ie,fe)};et.isTreeNode=1;function Te(e,n){if(!e)return[];var r=e.slice(),t=r.indexOf(n);return t>=0&&r.splice(t,1),r}function Le(e,n){var r=(e||[]).slice();return r.indexOf(n)===-1&&r.push(n),r}function Mt(e){return e.split("-")}function va(e,n){var r=[],t=xe(n,e);function o(){var s=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];s.forEach(function(u){var a=u.key,l=u.children;r.push(a),o(l)})}return o(t.children),r}function ha(e){if(e.parent){var n=Mt(e.pos);return Number(n[n.length-1])===e.parent.children.length-1}return!1}function ga(e){var n=Mt(e.pos);return Number(n[n.length-1])===0}function cn(e,n,r,t,o,s,u,a,l,i){var f,d=e.clientX,h=e.clientY,v=e.target.getBoundingClientRect(),y=v.top,m=v.height,g=(i==="rtl"?-1:1)*(((o==null?void 0:o.x)||0)-d),b=(g-12)/t,x=l.filter(function(j){var V;return(V=a[j])===null||V===void 0||(V=V.children)===null||V===void 0?void 0:V.length}),S=xe(a,r.eventKey);if(h<y+m/2){var N=u.findIndex(function(j){return j.key===S.key}),k=N<=0?0:N-1,$=u[k].key;S=xe(a,$)}var E=S.key,O=S,p=S.key,D=0,P=0;if(!x.includes(E))for(var H=0;H<b&&ha(S);H+=1)S=S.parent,P+=1;var A=n.data,I=S.node,L=!0;return ga(S)&&S.level===0&&h<y+m/2&&s({dragNode:A,dropNode:I,dropPosition:-1})&&S.key===r.eventKey?D=-1:(O.children||[]).length&&x.includes(p)?s({dragNode:A,dropNode:I,dropPosition:0})?D=0:L=!1:P===0?b>-1.5?s({dragNode:A,dropNode:I,dropPosition:1})?D=1:L=!1:s({dragNode:A,dropNode:I,dropPosition:0})?D=0:s({dragNode:A,dropNode:I,dropPosition:1})?D=1:L=!1:s({dragNode:A,dropNode:I,dropPosition:1})?D=1:L=!1,{dropPosition:D,dropLevelOffset:P,dropTargetKey:S.key,dropTargetPos:S.pos,dragOverNodeKey:p,dropContainerKey:D===0?null:((f=S.parent)===null||f===void 0?void 0:f.key)||null,dropAllowed:L}}function dn(e,n){if(e){var r=n.multiple;return r?e.slice():e.length?[e[0]]:e}}function Ct(e){if(!e)return null;var n;if(Array.isArray(e))n={checkedKeys:e,halfCheckedKeys:void 0};else if(Ge(e)==="object")n={checkedKeys:e.checked||void 0,halfCheckedKeys:e.halfChecked||void 0};else return He(!1,"`checkedKeys` is not an array or an object"),null;return n}function $t(e,n){var r=new Set;function t(o){if(!r.has(o)){var s=xe(n,o);if(s){r.add(o);var u=s.parent,a=s.node;a.disabled||u&&t(u.key)}}}return(e||[]).forEach(function(o){t(o)}),_e(r)}var pa=function(n){var r=n.dropPosition,t=n.dropLevelOffset,o=n.indent,s={pointerEvents:"none",position:"absolute",right:0,backgroundColor:"red",height:2};switch(r){case-1:s.top=0,s.left=-t*o;break;case 1:s.bottom=0,s.left=-t*o;break;case 0:s.bottom=0,s.left=o;break}return q.createElement("div",{style:s})};function Ln(e){if(e==null)throw new TypeError("Cannot destructure "+e)}function ya(e,n){var r=c.useState(!1),t=ee(r,2),o=t[0],s=t[1];je(function(){if(o)return e(),function(){n()}},[o]),je(function(){return s(!0),function(){s(!1)}},[])}var ma=["className","style","motion","motionNodes","motionType","onMotionStart","onMotionEnd","active","treeNodeRequiredProps"],ba=c.forwardRef(function(e,n){var r=e.className,t=e.style,o=e.motion,s=e.motionNodes,u=e.motionType,a=e.onMotionStart,l=e.onMotionEnd,i=e.active,f=e.treeNodeRequiredProps,d=tt(e,ma),h=c.useState(!0),v=ee(h,2),y=v[0],m=v[1],g=c.useContext(Pt),b=g.prefixCls,x=s&&u!=="hide";je(function(){s&&x!==y&&m(x)},[s]);var S=function(){s&&a()},N=c.useRef(!1),k=function(){s&&!N.current&&(N.current=!0,l())};ya(S,k);var $=function(O){x===O&&k()};return s?c.createElement(fr,ve({ref:n,visible:y},o,{motionAppear:u==="show",onVisibleChanged:$}),function(E,O){var p=E.className,D=E.style;return c.createElement("div",{ref:O,className:ne("".concat(b,"-treenode-motion"),p),style:D},s.map(function(P){var H=Object.assign({},(Ln(P.data),P.data)),A=P.title,I=P.key,L=P.isStart,j=P.isEnd;delete H.children;var V=Ze(I,f);return c.createElement(et,ve({},H,V,{title:A,active:i,data:P.data,key:I,isStart:L,isEnd:j}))}))}):c.createElement(et,ve({domRef:n,className:r,style:t},d,{active:i}))});function xa(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],r=e.length,t=n.length;if(Math.abs(r-t)!==1)return{add:!1,key:null};function o(s,u){var a=new Map;s.forEach(function(i){a.set(i,!0)});var l=u.filter(function(i){return!a.has(i)});return l.length===1?l[0]:null}return r<t?{add:!0,key:o(e,n)}:{add:!1,key:o(n,e)}}function un(e,n,r){var t=e.findIndex(function(a){return a.key===r}),o=e[t+1],s=n.findIndex(function(a){return a.key===r});if(o){var u=n.findIndex(function(a){return a.key===o.key});return n.slice(s+1,u)}return n.slice(s+1)}var Sa=["prefixCls","data","selectable","checkable","expandedKeys","selectedKeys","checkedKeys","loadedKeys","loadingKeys","halfCheckedKeys","keyEntities","disabled","dragging","dragOverNodeKey","dropPosition","motion","height","itemHeight","virtual","scrollWidth","focusable","activeItem","focused","tabIndex","onKeyDown","onFocus","onBlur","onActiveChange","onListChangeStart","onListChangeEnd"],fn={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0},Ca=function(){},Fe="RC_TREE_MOTION_".concat(Math.random()),Ot={key:Fe},Bn={key:Fe,level:0,index:0,pos:"0",node:Ot,nodes:[Ot]},vn={parent:null,children:[],pos:Bn.pos,data:Ot,title:null,key:Fe,isStart:[],isEnd:[]};function hn(e,n,r,t){return n===!1||!r?e:e.slice(0,Math.ceil(r/t)+1)}function gn(e){var n=e.key,r=e.pos;return nt(n,r)}function Ea(e){for(var n=String(e.data.key),r=e;r.parent;)r=r.parent,n="".concat(r.data.key," > ").concat(n);return n}var Na=c.forwardRef(function(e,n){var r=e.prefixCls,t=e.data;e.selectable,e.checkable;var o=e.expandedKeys,s=e.selectedKeys,u=e.checkedKeys,a=e.loadedKeys,l=e.loadingKeys,i=e.halfCheckedKeys,f=e.keyEntities,d=e.disabled,h=e.dragging,v=e.dragOverNodeKey,y=e.dropPosition,m=e.motion,g=e.height,b=e.itemHeight,x=e.virtual,S=e.scrollWidth,N=e.focusable,k=e.activeItem,$=e.focused,E=e.tabIndex,O=e.onKeyDown,p=e.onFocus,D=e.onBlur,P=e.onActiveChange,H=e.onListChangeStart,A=e.onListChangeEnd,I=tt(e,Sa),L=c.useRef(null),j=c.useRef(null);c.useImperativeHandle(n,function(){return{scrollTo:function(oe){L.current.scrollTo(oe)},getIndentWidth:function(){return j.current.offsetWidth}}});var V=c.useState(o),J=ee(V,2),re=J[0],U=J[1],C=c.useState(t),B=ee(C,2),M=B[0],z=B[1],he=c.useState(t),le=ee(he,2),$e=le[0],ce=le[1],de=c.useState([]),Ee=ee(de,2),te=Ee[0],Z=Ee[1],ae=c.useState(null),pe=ee(ae,2),ye=pe[0],Ne=pe[1],De=c.useRef(t);De.current=t;function Oe(){var G=De.current;z(G),ce(G),Z([]),Ne(null),A()}je(function(){U(o);var G=xa(re,o);if(G.key!==null)if(G.add){var oe=M.findIndex(function(Ce){var ge=Ce.key;return ge===G.key}),fe=hn(un(M,t,G.key),x,g,b),ke=M.slice();ke.splice(oe+1,0,vn),ce(ke),Z(fe),Ne("show")}else{var Ke=t.findIndex(function(Ce){var ge=Ce.key;return ge===G.key}),Pe=hn(un(t,M,G.key),x,g,b),me=t.slice();me.splice(Ke+1,0,vn),ce(me),Z(Pe),Ne("hide")}else M!==t&&(z(t),ce(t))},[o,t]),c.useEffect(function(){h||Oe()},[h]);var Ie=m?$e:t,Se={expandedKeys:o,selectedKeys:s,loadedKeys:a,loadingKeys:l,checkedKeys:u,halfCheckedKeys:i,dragOverNodeKey:v,dropPosition:y,keyEntities:f};return c.createElement(c.Fragment,null,$&&k&&c.createElement("span",{style:fn,"aria-live":"assertive"},Ea(k)),c.createElement("div",null,c.createElement("input",{style:fn,disabled:N===!1||d,tabIndex:N!==!1?E:null,onKeyDown:O,onFocus:p,onBlur:D,value:"",onChange:Ca,"aria-label":"for screen reader"})),c.createElement("div",{className:"".concat(r,"-treenode"),"aria-hidden":!0,style:{position:"absolute",pointerEvents:"none",visibility:"hidden",height:0,overflow:"hidden",border:0,padding:0}},c.createElement("div",{className:"".concat(r,"-indent")},c.createElement("div",{ref:j,className:"".concat(r,"-indent-unit")}))),c.createElement(On,ve({},I,{data:Ie,itemKey:gn,height:g,fullHeight:!1,virtual:x,itemHeight:b,scrollWidth:S,prefixCls:"".concat(r,"-list"),ref:L,role:"tree",onVisibleChange:function(oe){oe.every(function(fe){return gn(fe)!==Fe})&&Oe()}}),function(G){var oe=G.pos,fe=Object.assign({},(Ln(G.data),G.data)),ke=G.title,Ke=G.key,Pe=G.isStart,me=G.isEnd,Ce=nt(Ke,oe);delete fe.key,delete fe.children;var ge=Ze(Ce,Se);return c.createElement(ba,ve({},fe,ge,{title:ke,active:!!k&&Ke===k.key,pos:oe,data:G.data,isStart:Pe,isEnd:me,motion:m,motionNodes:Ke===Fe?te:null,motionType:ye,onMotionStart:H,onMotionEnd:Oe,treeNodeRequiredProps:Se,onMouseMove:function(){P(null)}}))}))}),Ka=10,Tt=function(e){vr(r,e);var n=hr(r);function r(){var t;En(this,r);for(var o=arguments.length,s=new Array(o),u=0;u<o;u++)s[u]=arguments[u];return t=n.call.apply(n,[this].concat(s)),w(F(t),"destroyed",!1),w(F(t),"delayedDragEnterLogic",void 0),w(F(t),"loadingRetryTimes",{}),w(F(t),"state",{keyEntities:{},indent:null,selectedKeys:[],checkedKeys:[],halfCheckedKeys:[],loadedKeys:[],loadingKeys:[],expandedKeys:[],draggingNodeKey:null,dragChildrenKeys:[],dropTargetKey:null,dropPosition:null,dropContainerKey:null,dropLevelOffset:null,dropTargetPos:null,dropAllowed:!0,dragOverNodeKey:null,treeData:[],flattenNodes:[],focused:!1,activeKey:null,listChanging:!1,prevProps:null,fieldNames:Ue()}),w(F(t),"dragStartMousePosition",null),w(F(t),"dragNodeProps",null),w(F(t),"currentMouseOverDroppableNodeKey",null),w(F(t),"listRef",c.createRef()),w(F(t),"onNodeDragStart",function(a,l){var i=t.state,f=i.expandedKeys,d=i.keyEntities,h=t.props.onDragStart,v=l.eventKey;t.dragNodeProps=l,t.dragStartMousePosition={x:a.clientX,y:a.clientY};var y=Te(f,v);t.setState({draggingNodeKey:v,dragChildrenKeys:va(v,d),indent:t.listRef.current.getIndentWidth()}),t.setExpandedKeys(y),window.addEventListener("dragend",t.onWindowDragEnd),h==null||h({event:a,node:se(l)})}),w(F(t),"onNodeDragEnter",function(a,l){var i=t.state,f=i.expandedKeys,d=i.keyEntities,h=i.dragChildrenKeys,v=i.flattenNodes,y=i.indent,m=t.props,g=m.onDragEnter,b=m.onExpand,x=m.allowDrop,S=m.direction,N=l.pos,k=l.eventKey;if(t.currentMouseOverDroppableNodeKey!==k&&(t.currentMouseOverDroppableNodeKey=k),!t.dragNodeProps){t.resetDragState();return}var $=cn(a,t.dragNodeProps,l,y,t.dragStartMousePosition,x,v,d,f,S),E=$.dropPosition,O=$.dropLevelOffset,p=$.dropTargetKey,D=$.dropContainerKey,P=$.dropTargetPos,H=$.dropAllowed,A=$.dragOverNodeKey;if(h.includes(p)||!H){t.resetDragState();return}if(t.delayedDragEnterLogic||(t.delayedDragEnterLogic={}),Object.keys(t.delayedDragEnterLogic).forEach(function(I){clearTimeout(t.delayedDragEnterLogic[I])}),t.dragNodeProps.eventKey!==l.eventKey&&(a.persist(),t.delayedDragEnterLogic[N]=window.setTimeout(function(){if(t.state.draggingNodeKey!==null){var I=_e(f),L=xe(d,l.eventKey);L&&(L.children||[]).length&&(I=Le(f,l.eventKey)),t.props.hasOwnProperty("expandedKeys")||t.setExpandedKeys(I),b==null||b(I,{node:se(l),expanded:!0,nativeEvent:a.nativeEvent})}},800)),t.dragNodeProps.eventKey===p&&O===0){t.resetDragState();return}t.setState({dragOverNodeKey:A,dropPosition:E,dropLevelOffset:O,dropTargetKey:p,dropContainerKey:D,dropTargetPos:P,dropAllowed:H}),g==null||g({event:a,node:se(l),expandedKeys:f})}),w(F(t),"onNodeDragOver",function(a,l){var i=t.state,f=i.dragChildrenKeys,d=i.flattenNodes,h=i.keyEntities,v=i.expandedKeys,y=i.indent,m=t.props,g=m.onDragOver,b=m.allowDrop,x=m.direction;if(t.dragNodeProps){var S=cn(a,t.dragNodeProps,l,y,t.dragStartMousePosition,b,d,h,v,x),N=S.dropPosition,k=S.dropLevelOffset,$=S.dropTargetKey,E=S.dropContainerKey,O=S.dropTargetPos,p=S.dropAllowed,D=S.dragOverNodeKey;f.includes($)||!p||(t.dragNodeProps.eventKey===$&&k===0?t.state.dropPosition===null&&t.state.dropLevelOffset===null&&t.state.dropTargetKey===null&&t.state.dropContainerKey===null&&t.state.dropTargetPos===null&&t.state.dropAllowed===!1&&t.state.dragOverNodeKey===null||t.resetDragState():N===t.state.dropPosition&&k===t.state.dropLevelOffset&&$===t.state.dropTargetKey&&E===t.state.dropContainerKey&&O===t.state.dropTargetPos&&p===t.state.dropAllowed&&D===t.state.dragOverNodeKey||t.setState({dropPosition:N,dropLevelOffset:k,dropTargetKey:$,dropContainerKey:E,dropTargetPos:O,dropAllowed:p,dragOverNodeKey:D}),g==null||g({event:a,node:se(l)}))}}),w(F(t),"onNodeDragLeave",function(a,l){t.currentMouseOverDroppableNodeKey===l.eventKey&&!a.currentTarget.contains(a.relatedTarget)&&(t.resetDragState(),t.currentMouseOverDroppableNodeKey=null);var i=t.props.onDragLeave;i==null||i({event:a,node:se(l)})}),w(F(t),"onWindowDragEnd",function(a){t.onNodeDragEnd(a,null,!0),window.removeEventListener("dragend",t.onWindowDragEnd)}),w(F(t),"onNodeDragEnd",function(a,l){var i=t.props.onDragEnd;t.setState({dragOverNodeKey:null}),t.cleanDragState(),i==null||i({event:a,node:se(l)}),t.dragNodeProps=null,window.removeEventListener("dragend",t.onWindowDragEnd)}),w(F(t),"onNodeDrop",function(a,l){var i,f=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,d=t.state,h=d.dragChildrenKeys,v=d.dropPosition,y=d.dropTargetKey,m=d.dropTargetPos,g=d.dropAllowed;if(g){var b=t.props.onDrop;if(t.setState({dragOverNodeKey:null}),t.cleanDragState(),y!==null){var x=Y(Y({},Ze(y,t.getTreeNodeRequiredProps())),{},{active:((i=t.getActiveItem())===null||i===void 0?void 0:i.key)===y,data:xe(t.state.keyEntities,y).node}),S=h.includes(y);He(!S,"Can not drop to dragNode's children node. This is a bug of rc-tree. Please report an issue.");var N=Mt(m),k={event:a,node:se(x),dragNode:t.dragNodeProps?se(t.dragNodeProps):null,dragNodesKeys:[t.dragNodeProps.eventKey].concat(h),dropToGap:v!==0,dropPosition:v+Number(N[N.length-1])};f||b==null||b(k),t.dragNodeProps=null}}}),w(F(t),"cleanDragState",function(){var a=t.state.draggingNodeKey;a!==null&&t.setState({draggingNodeKey:null,dropPosition:null,dropContainerKey:null,dropTargetKey:null,dropLevelOffset:null,dropAllowed:!0,dragOverNodeKey:null}),t.dragStartMousePosition=null,t.currentMouseOverDroppableNodeKey=null}),w(F(t),"triggerExpandActionExpand",function(a,l){var i=t.state,f=i.expandedKeys,d=i.flattenNodes,h=l.expanded,v=l.key,y=l.isLeaf;if(!(y||a.shiftKey||a.metaKey||a.ctrlKey)){var m=d.filter(function(b){return b.key===v})[0],g=se(Y(Y({},Ze(v,t.getTreeNodeRequiredProps())),{},{data:m.data}));t.setExpandedKeys(h?Te(f,v):Le(f,v)),t.onNodeExpand(a,g)}}),w(F(t),"onNodeClick",function(a,l){var i=t.props,f=i.onClick,d=i.expandAction;d==="click"&&t.triggerExpandActionExpand(a,l),f==null||f(a,l)}),w(F(t),"onNodeDoubleClick",function(a,l){var i=t.props,f=i.onDoubleClick,d=i.expandAction;d==="doubleClick"&&t.triggerExpandActionExpand(a,l),f==null||f(a,l)}),w(F(t),"onNodeSelect",function(a,l){var i=t.state.selectedKeys,f=t.state,d=f.keyEntities,h=f.fieldNames,v=t.props,y=v.onSelect,m=v.multiple,g=l.selected,b=l[h.key],x=!g;x?m?i=Le(i,b):i=[b]:i=Te(i,b);var S=i.map(function(N){var k=xe(d,N);return k?k.node:null}).filter(Boolean);t.setUncontrolledState({selectedKeys:i}),y==null||y(i,{event:"select",selected:x,node:l,selectedNodes:S,nativeEvent:a.nativeEvent})}),w(F(t),"onNodeCheck",function(a,l,i){var f=t.state,d=f.keyEntities,h=f.checkedKeys,v=f.halfCheckedKeys,y=t.props,m=y.checkStrictly,g=y.onCheck,b=l.key,x,S={event:"check",node:l,checked:i,nativeEvent:a.nativeEvent};if(m){var N=i?Le(h,b):Te(h,b),k=Te(v,b);x={checked:N,halfChecked:k},S.checkedNodes=N.map(function(P){return xe(d,P)}).filter(Boolean).map(function(P){return P.node}),t.setUncontrolledState({checkedKeys:N})}else{var $=St([].concat(_e(h),[b]),!0,d),E=$.checkedKeys,O=$.halfCheckedKeys;if(!i){var p=new Set(E);p.delete(b);var D=St(Array.from(p),{checked:!1,halfCheckedKeys:O},d);E=D.checkedKeys,O=D.halfCheckedKeys}x=E,S.checkedNodes=[],S.checkedNodesPositions=[],S.halfCheckedKeys=O,E.forEach(function(P){var H=xe(d,P);if(H){var A=H.node,I=H.pos;S.checkedNodes.push(A),S.checkedNodesPositions.push({node:A,pos:I})}}),t.setUncontrolledState({checkedKeys:E},!1,{halfCheckedKeys:O})}g==null||g(x,S)}),w(F(t),"onNodeLoad",function(a){var l,i=a.key,f=t.state.keyEntities,d=xe(f,i);if(!(d!=null&&(l=d.children)!==null&&l!==void 0&&l.length)){var h=new Promise(function(v,y){t.setState(function(m){var g=m.loadedKeys,b=g===void 0?[]:g,x=m.loadingKeys,S=x===void 0?[]:x,N=t.props,k=N.loadData,$=N.onLoad;if(!k||b.includes(i)||S.includes(i))return null;var E=k(a);return E.then(function(){var O=t.state.loadedKeys,p=Le(O,i);$==null||$(p,{event:"load",node:a}),t.setUncontrolledState({loadedKeys:p}),t.setState(function(D){return{loadingKeys:Te(D.loadingKeys,i)}}),v()}).catch(function(O){if(t.setState(function(D){return{loadingKeys:Te(D.loadingKeys,i)}}),t.loadingRetryTimes[i]=(t.loadingRetryTimes[i]||0)+1,t.loadingRetryTimes[i]>=Ka){var p=t.state.loadedKeys;He(!1,"Retry for `loadData` many times but still failed. No more retry."),t.setUncontrolledState({loadedKeys:Le(p,i)}),v()}y(O)}),{loadingKeys:Le(S,i)}})});return h.catch(function(){}),h}}),w(F(t),"onNodeMouseEnter",function(a,l){var i=t.props.onMouseEnter;i==null||i({event:a,node:l})}),w(F(t),"onNodeMouseLeave",function(a,l){var i=t.props.onMouseLeave;i==null||i({event:a,node:l})}),w(F(t),"onNodeContextMenu",function(a,l){var i=t.props.onRightClick;i&&(a.preventDefault(),i({event:a,node:l}))}),w(F(t),"onFocus",function(){var a=t.props.onFocus;t.setState({focused:!0});for(var l=arguments.length,i=new Array(l),f=0;f<l;f++)i[f]=arguments[f];a==null||a.apply(void 0,i)}),w(F(t),"onBlur",function(){var a=t.props.onBlur;t.setState({focused:!1}),t.onActiveChange(null);for(var l=arguments.length,i=new Array(l),f=0;f<l;f++)i[f]=arguments[f];a==null||a.apply(void 0,i)}),w(F(t),"getTreeNodeRequiredProps",function(){var a=t.state,l=a.expandedKeys,i=a.selectedKeys,f=a.loadedKeys,d=a.loadingKeys,h=a.checkedKeys,v=a.halfCheckedKeys,y=a.dragOverNodeKey,m=a.dropPosition,g=a.keyEntities;return{expandedKeys:l||[],selectedKeys:i||[],loadedKeys:f||[],loadingKeys:d||[],checkedKeys:h||[],halfCheckedKeys:v||[],dragOverNodeKey:y,dropPosition:m,keyEntities:g}}),w(F(t),"setExpandedKeys",function(a){var l=t.state,i=l.treeData,f=l.fieldNames,d=xt(i,a,f);t.setUncontrolledState({expandedKeys:a,flattenNodes:d},!0)}),w(F(t),"onNodeExpand",function(a,l){var i=t.state.expandedKeys,f=t.state,d=f.listChanging,h=f.fieldNames,v=t.props,y=v.onExpand,m=v.loadData,g=l.expanded,b=l[h.key];if(!d){var x=i.includes(b),S=!g;if(He(g&&x||!g&&!x,"Expand state not sync with index check"),i=S?Le(i,b):Te(i,b),t.setExpandedKeys(i),y==null||y(i,{node:l,expanded:S,nativeEvent:a.nativeEvent}),S&&m){var N=t.onNodeLoad(l);N&&N.then(function(){var k=xt(t.state.treeData,i,h);t.setUncontrolledState({flattenNodes:k})}).catch(function(){var k=t.state.expandedKeys,$=Te(k,b);t.setExpandedKeys($)})}}}),w(F(t),"onListChangeStart",function(){t.setUncontrolledState({listChanging:!0})}),w(F(t),"onListChangeEnd",function(){setTimeout(function(){t.setUncontrolledState({listChanging:!1})})}),w(F(t),"onActiveChange",function(a){var l=t.state.activeKey,i=t.props,f=i.onActiveChange,d=i.itemScrollOffset,h=d===void 0?0:d;l!==a&&(t.setState({activeKey:a}),a!==null&&t.scrollTo({key:a,offset:h}),f==null||f(a))}),w(F(t),"getActiveItem",function(){var a=t.state,l=a.activeKey,i=a.flattenNodes;return l===null?null:i.find(function(f){var d=f.key;return d===l})||null}),w(F(t),"offsetActiveKey",function(a){var l=t.state,i=l.flattenNodes,f=l.activeKey,d=i.findIndex(function(y){var m=y.key;return m===f});d===-1&&a<0&&(d=i.length),d=(d+a+i.length)%i.length;var h=i[d];if(h){var v=h.key;t.onActiveChange(v)}else t.onActiveChange(null)}),w(F(t),"onKeyDown",function(a){var l=t.state,i=l.activeKey,f=l.expandedKeys,d=l.checkedKeys,h=l.fieldNames,v=t.props,y=v.onKeyDown,m=v.checkable,g=v.selectable;switch(a.which){case Ae.UP:{t.offsetActiveKey(-1),a.preventDefault();break}case Ae.DOWN:{t.offsetActiveKey(1),a.preventDefault();break}}var b=t.getActiveItem();if(b&&b.data){var x=t.getTreeNodeRequiredProps(),S=b.data.isLeaf===!1||!!(b.data[h.children]||[]).length,N=se(Y(Y({},Ze(i,x)),{},{data:b.data,active:!0}));switch(a.which){case Ae.LEFT:{S&&f.includes(i)?t.onNodeExpand({},N):b.parent&&t.onActiveChange(b.parent.key),a.preventDefault();break}case Ae.RIGHT:{S&&!f.includes(i)?t.onNodeExpand({},N):b.children&&b.children.length&&t.onActiveChange(b.children[0].key),a.preventDefault();break}case Ae.ENTER:case Ae.SPACE:{m&&!N.disabled&&N.checkable!==!1&&!N.disableCheckbox?t.onNodeCheck({},N,!d.includes(i)):!m&&g&&!N.disabled&&N.selectable!==!1&&t.onNodeSelect({},N);break}}}y==null||y(a)}),w(F(t),"setUncontrolledState",function(a){var l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null;if(!t.destroyed){var f=!1,d=!0,h={};Object.keys(a).forEach(function(v){if(t.props.hasOwnProperty(v)){d=!1;return}f=!0,h[v]=a[v]}),f&&(!l||d)&&t.setState(Y(Y({},h),i))}}),w(F(t),"scrollTo",function(a){t.listRef.current.scrollTo(a)}),t}return Cn(r,[{key:"componentDidMount",value:function(){this.destroyed=!1,this.onUpdated()}},{key:"componentDidUpdate",value:function(){this.onUpdated()}},{key:"onUpdated",value:function(){var o=this.props,s=o.activeKey,u=o.itemScrollOffset,a=u===void 0?0:u;s!==void 0&&s!==this.state.activeKey&&(this.setState({activeKey:s}),s!==null&&this.scrollTo({key:s,offset:a}))}},{key:"componentWillUnmount",value:function(){window.removeEventListener("dragend",this.onWindowDragEnd),this.destroyed=!0}},{key:"resetDragState",value:function(){this.setState({dragOverNodeKey:null,dropPosition:null,dropLevelOffset:null,dropTargetKey:null,dropContainerKey:null,dropTargetPos:null,dropAllowed:!1})}},{key:"render",value:function(){var o=this.state,s=o.focused,u=o.flattenNodes,a=o.keyEntities,l=o.draggingNodeKey,i=o.activeKey,f=o.dropLevelOffset,d=o.dropContainerKey,h=o.dropTargetKey,v=o.dropPosition,y=o.dragOverNodeKey,m=o.indent,g=this.props,b=g.prefixCls,x=g.className,S=g.style,N=g.showLine,k=g.focusable,$=g.tabIndex,E=$===void 0?0:$,O=g.selectable,p=g.showIcon,D=g.icon,P=g.switcherIcon,H=g.draggable,A=g.checkable,I=g.checkStrictly,L=g.disabled,j=g.motion,V=g.loadData,J=g.filterTreeNode,re=g.height,U=g.itemHeight,C=g.scrollWidth,B=g.virtual,M=g.titleRender,z=g.dropIndicatorRender,he=g.onContextMenu,le=g.onScroll,$e=g.direction,ce=g.rootClassName,de=g.rootStyle,Ee=Kn(this.props,{aria:!0,data:!0}),te;H&&(Ge(H)==="object"?te=H:typeof H=="function"?te={nodeDraggable:H}:te={});var Z={prefixCls:b,selectable:O,showIcon:p,icon:D,switcherIcon:P,draggable:te,draggingNodeKey:l,checkable:A,checkStrictly:I,disabled:L,keyEntities:a,dropLevelOffset:f,dropContainerKey:d,dropTargetKey:h,dropPosition:v,dragOverNodeKey:y,indent:m,direction:$e,dropIndicatorRender:z,loadData:V,filterTreeNode:J,titleRender:M,onNodeClick:this.onNodeClick,onNodeDoubleClick:this.onNodeDoubleClick,onNodeExpand:this.onNodeExpand,onNodeSelect:this.onNodeSelect,onNodeCheck:this.onNodeCheck,onNodeLoad:this.onNodeLoad,onNodeMouseEnter:this.onNodeMouseEnter,onNodeMouseLeave:this.onNodeMouseLeave,onNodeContextMenu:this.onNodeContextMenu,onNodeDragStart:this.onNodeDragStart,onNodeDragEnter:this.onNodeDragEnter,onNodeDragOver:this.onNodeDragOver,onNodeDragLeave:this.onNodeDragLeave,onNodeDragEnd:this.onNodeDragEnd,onNodeDrop:this.onNodeDrop};return c.createElement(Pt.Provider,{value:Z},c.createElement("div",{className:ne(b,x,ce,w(w(w({},"".concat(b,"-show-line"),N),"".concat(b,"-focused"),s),"".concat(b,"-active-focused"),i!==null)),style:de},c.createElement(Na,ve({ref:this.listRef,prefixCls:b,style:S,data:u,disabled:L,selectable:O,checkable:!!A,motion:j,dragging:l!==null,height:re,itemHeight:U,virtual:B,focusable:k,focused:s,tabIndex:E,activeItem:this.getActiveItem(),onFocus:this.onFocus,onBlur:this.onBlur,onKeyDown:this.onKeyDown,onActiveChange:this.onActiveChange,onListChangeStart:this.onListChangeStart,onListChangeEnd:this.onListChangeEnd,onContextMenu:he,onScroll:le,scrollWidth:C},this.getTreeNodeRequiredProps(),Ee))))}}],[{key:"getDerivedStateFromProps",value:function(o,s){var u=s.prevProps,a={prevProps:o};function l(E){return!u&&o.hasOwnProperty(E)||u&&u[E]!==o[E]}var i,f=s.fieldNames;if(l("fieldNames")&&(f=Ue(o.fieldNames),a.fieldNames=f),l("treeData")?i=o.treeData:l("children")&&(He(!1,"`children` of Tree is deprecated. Please use `treeData` instead."),i=Mn(o.children)),i){a.treeData=i;var d=Tn(i,{fieldNames:f});a.keyEntities=Y(w({},Fe,Bn),d.keyEntities)}var h=a.keyEntities||s.keyEntities;if(l("expandedKeys")||u&&l("autoExpandParent"))a.expandedKeys=o.autoExpandParent||!u&&o.defaultExpandParent?$t(o.expandedKeys,h):o.expandedKeys;else if(!u&&o.defaultExpandAll){var v=Y({},h);delete v[Fe];var y=[];Object.keys(v).forEach(function(E){var O=v[E];O.children&&O.children.length&&y.push(O.key)}),a.expandedKeys=y}else!u&&o.defaultExpandedKeys&&(a.expandedKeys=o.autoExpandParent||o.defaultExpandParent?$t(o.defaultExpandedKeys,h):o.defaultExpandedKeys);if(a.expandedKeys||delete a.expandedKeys,i||a.expandedKeys){var m=xt(i||s.treeData,a.expandedKeys||s.expandedKeys,f);a.flattenNodes=m}if(o.selectable&&(l("selectedKeys")?a.selectedKeys=dn(o.selectedKeys,o):!u&&o.defaultSelectedKeys&&(a.selectedKeys=dn(o.defaultSelectedKeys,o))),o.checkable){var g;if(l("checkedKeys")?g=Ct(o.checkedKeys)||{}:!u&&o.defaultCheckedKeys?g=Ct(o.defaultCheckedKeys)||{}:i&&(g=Ct(o.checkedKeys)||{checkedKeys:s.checkedKeys,halfCheckedKeys:s.halfCheckedKeys}),g){var b=g,x=b.checkedKeys,S=x===void 0?[]:x,N=b.halfCheckedKeys,k=N===void 0?[]:N;if(!o.checkStrictly){var $=St(S,!0,h);S=$.checkedKeys,k=$.halfCheckedKeys}a.checkedKeys=S,a.halfCheckedKeys=k}}return l("loadedKeys")&&(a.loadedKeys=o.loadedKeys),a}}]),r}(c.Component);w(Tt,"defaultProps",{prefixCls:"rc-tree",showLine:!1,showIcon:!0,selectable:!0,multiple:!1,checkable:!1,disabled:!1,checkStrictly:!1,draggable:!1,defaultExpandParent:!0,autoExpandParent:!1,defaultExpandAll:!1,defaultExpandedKeys:[],defaultCheckedKeys:[],defaultSelectedKeys:[],dropIndicatorRender:pa,allowDrop:function(){return!0},expandAction:!1});w(Tt,"TreeNode",et);var wa={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file",theme:"outlined"};const ka=wa;var $a=function(n,r){return c.createElement(We,ve({},n,{ref:r,icon:ka}))},Oa=c.forwardRef($a);const Hn=Oa;var Ra={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 444H820V330.4c0-17.7-14.3-32-32-32H473L355.7 186.2a8.15 8.15 0 00-5.5-2.2H96c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h698c13 0 24.8-7.9 29.7-20l134-332c1.5-3.8 2.3-7.9 2.3-12 0-17.7-14.3-32-32-32zM136 256h188.5l119.6 114.4H748V444H238c-13 0-24.8 7.9-29.7 20L136 643.2V256zm635.3 512H159l103.3-256h612.4L771.3 768z"}}]},name:"folder-open",theme:"outlined"};const _a=Ra;var Da=function(n,r){return c.createElement(We,ve({},n,{ref:r,icon:_a}))},Pa=c.forwardRef(Da);const Ma=Pa;var Ta={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 298.4H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z"}}]},name:"folder",theme:"outlined"};const ja=Ta;var Ia=function(n,r){return c.createElement(We,ve({},n,{ref:r,icon:ja}))},La=c.forwardRef(Ia);const Ba=La;var Ha={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M300 276.5a56 56 0 1056-97 56 56 0 00-56 97zm0 284a56 56 0 1056-97 56 56 0 00-56 97zM640 228a56 56 0 10112 0 56 56 0 00-112 0zm0 284a56 56 0 10112 0 56 56 0 00-112 0zM300 844.5a56 56 0 1056-97 56 56 0 00-56 97zM640 796a56 56 0 10112 0 56 56 0 00-112 0z"}}]},name:"holder",theme:"outlined"};const za=Ha;var Aa=function(n,r){return c.createElement(We,ve({},n,{ref:r,icon:za}))},Fa=c.forwardRef(Aa);const Wa=Fa,qa=({treeCls:e,treeNodeCls:n,directoryNodeSelectedBg:r,directoryNodeSelectedColor:t,motionDurationMid:o,borderRadius:s,controlItemBgHover:u})=>({[`${e}${e}-directory ${n}`]:{[`${e}-node-content-wrapper`]:{position:"static",[`&:has(${e}-drop-indicator)`]:{position:"relative"},[`> *:not(${e}-drop-indicator)`]:{position:"relative"},"&:hover":{background:"transparent"},"&:before":{position:"absolute",inset:0,transition:`background-color ${o}`,content:'""',borderRadius:s},"&:hover:before":{background:u}},[`${e}-switcher, ${e}-checkbox, ${e}-draggable-icon`]:{zIndex:1},"&-selected":{background:r,borderRadius:s,[`${e}-switcher, ${e}-draggable-icon`]:{color:t},[`${e}-node-content-wrapper`]:{color:t,background:"transparent","&, &:hover":{color:t},"&:before, &:hover:before":{background:r}}}}}),Va=new pr("ant-tree-node-fx-do-not-use",{"0%":{opacity:0},"100%":{opacity:1}}),Xa=(e,n)=>({[`.${e}-switcher-icon`]:{display:"inline-block",fontSize:10,verticalAlign:"baseline",svg:{transition:`transform ${n.motionDurationSlow}`}}}),Ua=(e,n)=>({[`.${e}-drop-indicator`]:{position:"absolute",zIndex:1,height:2,backgroundColor:n.colorPrimary,borderRadius:1,pointerEvents:"none","&:after":{position:"absolute",top:-3,insetInlineStart:-6,width:8,height:8,backgroundColor:"transparent",border:`${Qe(n.lineWidthBold)} solid ${n.colorPrimary}`,borderRadius:"50%",content:'""'}}}),Ga=(e,n)=>{const{treeCls:r,treeNodeCls:t,treeNodePadding:o,titleHeight:s,indentSize:u,nodeSelectedBg:a,nodeHoverBg:l,colorTextQuaternary:i,controlItemBgActiveDisabled:f}=n;return{[r]:Object.assign(Object.assign({},Je(n)),{"--rc-virtual-list-scrollbar-bg":n.colorSplit,background:n.colorBgContainer,borderRadius:n.borderRadius,transition:`background-color ${n.motionDurationSlow}`,"&-rtl":{direction:"rtl"},[`&${r}-rtl ${r}-switcher_close ${r}-switcher-icon svg`]:{transform:"rotate(90deg)"},[`&-focused:not(:hover):not(${r}-active-focused)`]:Nn(n),[`${r}-list-holder-inner`]:{alignItems:"flex-start"},[`&${r}-block-node`]:{[`${r}-list-holder-inner`]:{alignItems:"stretch",[`${r}-node-content-wrapper`]:{flex:"auto"},[`${t}.dragging:after`]:{position:"absolute",inset:0,border:`1px solid ${n.colorPrimary}`,opacity:0,animationName:Va,animationDuration:n.motionDurationSlow,animationPlayState:"running",animationFillMode:"forwards",content:'""',pointerEvents:"none",borderRadius:n.borderRadius}}},[t]:{display:"flex",alignItems:"flex-start",marginBottom:o,lineHeight:Qe(s),position:"relative","&:before":{content:'""',position:"absolute",zIndex:1,insetInlineStart:0,width:"100%",top:"100%",height:o},[`&-disabled ${r}-node-content-wrapper`]:{color:n.colorTextDisabled,cursor:"not-allowed","&:hover":{background:"transparent"}},[`${r}-checkbox-disabled + ${r}-node-selected,&${t}-disabled${t}-selected ${r}-node-content-wrapper`]:{backgroundColor:f},[`${r}-checkbox-disabled`]:{pointerEvents:"unset"},[`&:not(${t}-disabled)`]:{[`${r}-node-content-wrapper`]:{"&:hover":{color:n.nodeHoverColor}}},[`&-active ${r}-node-content-wrapper`]:{background:n.controlItemBgHover},[`&:not(${t}-disabled).filter-node ${r}-title`]:{color:n.colorPrimary,fontWeight:n.fontWeightStrong},"&-draggable":{cursor:"grab",[`${r}-draggable-icon`]:{flexShrink:0,width:s,textAlign:"center",visibility:"visible",color:i},[`&${t}-disabled ${r}-draggable-icon`]:{visibility:"hidden"}}},[`${r}-indent`]:{alignSelf:"stretch",whiteSpace:"nowrap",userSelect:"none","&-unit":{display:"inline-block",width:u}},[`${r}-draggable-icon`]:{visibility:"hidden"},[`${r}-switcher, ${r}-checkbox`]:{marginInlineEnd:n.calc(n.calc(s).sub(n.controlInteractiveSize)).div(2).equal()},[`${r}-switcher`]:Object.assign(Object.assign({},Xa(e,n)),{position:"relative",flex:"none",alignSelf:"stretch",width:s,textAlign:"center",cursor:"pointer",userSelect:"none",transition:`all ${n.motionDurationSlow}`,"&-noop":{cursor:"unset"},"&:before":{pointerEvents:"none",content:'""',width:s,height:s,position:"absolute",left:{_skip_check_:!0,value:0},top:0,borderRadius:n.borderRadius,transition:`all ${n.motionDurationSlow}`},[`&:not(${r}-switcher-noop):hover:before`]:{backgroundColor:n.colorBgTextHover},[`&_close ${r}-switcher-icon svg`]:{transform:"rotate(-90deg)"},"&-loading-icon":{color:n.colorPrimary},"&-leaf-line":{position:"relative",zIndex:1,display:"inline-block",width:"100%",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:n.calc(s).div(2).equal(),bottom:n.calc(o).mul(-1).equal(),marginInlineStart:-1,borderInlineEnd:`1px solid ${n.colorBorder}`,content:'""'},"&:after":{position:"absolute",width:n.calc(n.calc(s).div(2).equal()).mul(.8).equal(),height:n.calc(s).div(2).equal(),borderBottom:`1px solid ${n.colorBorder}`,content:'""'}}}),[`${r}-node-content-wrapper`]:Object.assign(Object.assign({position:"relative",minHeight:s,paddingBlock:0,paddingInline:n.paddingXS,background:"transparent",borderRadius:n.borderRadius,cursor:"pointer",transition:`all ${n.motionDurationMid}, border 0s, line-height 0s, box-shadow 0s`},Ua(e,n)),{"&:hover":{backgroundColor:l},[`&${r}-node-selected`]:{color:n.nodeSelectedColor,backgroundColor:a},[`${r}-iconEle`]:{display:"inline-block",width:s,height:s,textAlign:"center",verticalAlign:"top","&:empty":{display:"none"}}}),[`${r}-unselectable ${r}-node-content-wrapper:hover`]:{backgroundColor:"transparent"},[`${t}.drop-container > [draggable]`]:{boxShadow:`0 0 0 2px ${n.colorPrimary}`},"&-show-line":{[`${r}-indent-unit`]:{position:"relative",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:n.calc(s).div(2).equal(),bottom:n.calc(o).mul(-1).equal(),borderInlineEnd:`1px solid ${n.colorBorder}`,content:'""'},"&-end:before":{display:"none"}},[`${r}-switcher`]:{background:"transparent","&-line-icon":{verticalAlign:"-0.15em"}}},[`${t}-leaf-last ${r}-switcher-leaf-line:before`]:{top:"auto !important",bottom:"auto !important",height:`${Qe(n.calc(s).div(2).equal())} !important`}})}},Ya=(e,n,r=!0)=>{const t=`.${e}`,o=`${t}-treenode`,s=n.calc(n.paddingXS).div(2).equal(),u=_t(n,{treeCls:t,treeNodeCls:o,treeNodePadding:s});return[Ga(e,u),r&&qa(u)].filter(Boolean)},Ja=e=>{const{controlHeightSM:n,controlItemBgHover:r,controlItemBgActive:t}=e,o=n;return{titleHeight:o,indentSize:o,nodeHoverBg:r,nodeHoverColor:e.colorText,nodeSelectedBg:t,nodeSelectedColor:e.colorText}},Za=e=>{const{colorTextLightSolid:n,colorPrimary:r}=e;return Object.assign(Object.assign({},Ja(e)),{directoryNodeSelectedColor:n,directoryNodeSelectedBg:r})},Qa=Rt("Tree",(e,{prefixCls:n})=>[{[e.componentCls]:In(`${n}-checkbox`,e)},Ya(n,e),gr(e)],Za),pn=4;function eo(e){const{dropPosition:n,dropLevelOffset:r,prefixCls:t,indent:o,direction:s="ltr"}=e,u=s==="ltr"?"left":"right",a=s==="ltr"?"right":"left",l={[u]:-r*o+pn,[a]:0};switch(n){case-1:l.top=-3;break;case 1:l.bottom=-3;break;default:l.bottom=-3,l[u]=o+pn;break}return q.createElement("div",{style:l,className:`${t}-drop-indicator`})}var to={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"filled"};const no=to;var ro=function(n,r){return c.createElement(We,ve({},n,{ref:r,icon:no}))},ao=c.forwardRef(ro);const oo=ao;var io={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h368c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"minus-square",theme:"outlined"};const so=io;var lo=function(n,r){return c.createElement(We,ve({},n,{ref:r,icon:so}))},co=c.forwardRef(lo);const uo=co;var fo={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"plus-square",theme:"outlined"};const vo=fo;var ho=function(n,r){return c.createElement(We,ve({},n,{ref:r,icon:vo}))},go=c.forwardRef(ho);const po=go,yo=e=>{var n,r;const{prefixCls:t,switcherIcon:o,treeNodeProps:s,showLine:u,switcherLoadingIcon:a}=e,{isLeaf:l,expanded:i,loading:f}=s;if(f)return c.isValidElement(a)?a:c.createElement(yr,{className:`${t}-switcher-loading-icon`});let d;if(u&&typeof u=="object"&&(d=u.showLeafIcon),l){if(!u)return null;if(typeof d!="boolean"&&d){const y=typeof d=="function"?d(s):d,m=`${t}-switcher-line-custom-icon`;return c.isValidElement(y)?wt(y,{className:ne((n=y.props)===null||n===void 0?void 0:n.className,m)}):y}return d?c.createElement(Hn,{className:`${t}-switcher-line-icon`}):c.createElement("span",{className:`${t}-switcher-leaf-line`})}const h=`${t}-switcher-icon`,v=typeof o=="function"?o(s):o;return c.isValidElement(v)?wt(v,{className:ne((r=v.props)===null||r===void 0?void 0:r.className,h)}):v!==void 0?v:u?i?c.createElement(uo,{className:`${t}-switcher-line-icon`}):c.createElement(po,{className:`${t}-switcher-line-icon`}):c.createElement(oo,{className:h})},mo=yo,bo=q.forwardRef((e,n)=>{var r;const{getPrefixCls:t,direction:o,virtual:s,tree:u}=q.useContext(Dt),{prefixCls:a,className:l,showIcon:i=!1,showLine:f,switcherIcon:d,switcherLoadingIcon:h,blockNode:v=!1,children:y,checkable:m=!1,selectable:g=!0,draggable:b,motion:x,style:S}=e,N=t("tree",a),k=t(),$=x??Object.assign(Object.assign({},mr(k)),{motionAppear:!1}),E=Object.assign(Object.assign({},e),{checkable:m,selectable:g,showIcon:i,motion:$,blockNode:v,showLine:!!f,dropIndicatorRender:eo}),[O,p,D]=Qa(N),[,P]=br(),H=P.paddingXS/2+(((r=P.Tree)===null||r===void 0?void 0:r.titleHeight)||P.controlHeightSM),A=q.useMemo(()=>{if(!b)return!1;let L={};switch(typeof b){case"function":L.nodeDraggable=b;break;case"object":L=Object.assign({},b);break}return L.icon!==!1&&(L.icon=L.icon||q.createElement(Wa,null)),L},[b]),I=L=>q.createElement(mo,{prefixCls:N,switcherIcon:d,switcherLoadingIcon:h,treeNodeProps:L,showLine:f});return O(q.createElement(Tt,Object.assign({itemHeight:H,ref:n,virtual:s},E,{style:Object.assign(Object.assign({},u==null?void 0:u.style),S),prefixCls:N,className:ne({[`${N}-icon-hide`]:!i,[`${N}-block-node`]:v,[`${N}-unselectable`]:!g,[`${N}-rtl`]:o==="rtl"},u==null?void 0:u.className,l,p,D),direction:o,checkable:m&&q.createElement("span",{className:`${N}-checkbox-inner`}),selectable:g,switcherIcon:I,draggable:A}),y))}),zn=bo,yn=0,Et=1,mn=2;function jt(e,n,r){const{key:t,children:o}=r;function s(u){const a=u[t],l=u[o];n(a,u)!==!1&&jt(l||[],n,r)}e.forEach(s)}function xo({treeData:e,expandedKeys:n,startKey:r,endKey:t,fieldNames:o}){const s=[];let u=yn;if(r&&r===t)return[r];if(!r||!t)return[];function a(l){return l===r||l===t}return jt(e,l=>{if(u===mn)return!1;if(a(l)){if(s.push(l),u===yn)u=Et;else if(u===Et)return u=mn,!1}else u===Et&&s.push(l);return n.includes(l)},Ue(o)),s}function Nt(e,n,r){const t=_e(n),o=[];return jt(e,(s,u)=>{const a=t.indexOf(s);return a!==-1&&(o.push(u),t.splice(a,1)),!!t.length},Ue(r)),o}var bn=globalThis&&globalThis.__rest||function(e,n){var r={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(r[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,t=Object.getOwnPropertySymbols(e);o<t.length;o++)n.indexOf(t[o])<0&&Object.prototype.propertyIsEnumerable.call(e,t[o])&&(r[t[o]]=e[t[o]]);return r};function So(e){const{isLeaf:n,expanded:r}=e;return n?c.createElement(Hn,null):r?c.createElement(Ma,null):c.createElement(Ba,null)}function xn({treeData:e,children:n}){return e||Mn(n)}const Co=(e,n)=>{var{defaultExpandAll:r,defaultExpandParent:t,defaultExpandedKeys:o}=e,s=bn(e,["defaultExpandAll","defaultExpandParent","defaultExpandedKeys"]);const u=c.useRef(null),a=c.useRef(null),l=()=>{const{keyEntities:O}=Tn(xn(s));let p;return r?p=Object.keys(O):t?p=$t(s.expandedKeys||o||[],O):p=s.expandedKeys||o||[],p},[i,f]=c.useState(s.selectedKeys||s.defaultSelectedKeys||[]),[d,h]=c.useState(()=>l());c.useEffect(()=>{"selectedKeys"in s&&f(s.selectedKeys)},[s.selectedKeys]),c.useEffect(()=>{"expandedKeys"in s&&h(s.expandedKeys)},[s.expandedKeys]);const v=(O,p)=>{var D;return"expandedKeys"in s||h(O),(D=s.onExpand)===null||D===void 0?void 0:D.call(s,O,p)},y=(O,p)=>{var D;const{multiple:P,fieldNames:H}=s,{node:A,nativeEvent:I}=p,{key:L=""}=A,j=xn(s),V=Object.assign(Object.assign({},p),{selected:!0}),J=(I==null?void 0:I.ctrlKey)||(I==null?void 0:I.metaKey),re=I==null?void 0:I.shiftKey;let U;P&&J?(U=O,u.current=L,a.current=U,V.selectedNodes=Nt(j,U,H)):P&&re?(U=Array.from(new Set([].concat(_e(a.current||[]),_e(xo({treeData:j,expandedKeys:d,startKey:L,endKey:u.current,fieldNames:H}))))),V.selectedNodes=Nt(j,U,H)):(U=[L],u.current=L,a.current=U,V.selectedNodes=Nt(j,U,H)),(D=s.onSelect)===null||D===void 0||D.call(s,U,V),"selectedKeys"in s||f(U)},{getPrefixCls:m,direction:g}=c.useContext(Dt),{prefixCls:b,className:x,showIcon:S=!0,expandAction:N="click"}=s,k=bn(s,["prefixCls","className","showIcon","expandAction"]),$=m("tree",b),E=ne(`${$}-directory`,{[`${$}-directory-rtl`]:g==="rtl"},x);return c.createElement(zn,Object.assign({icon:So,ref:n,blockNode:!0},k,{showIcon:S,expandAction:N,prefixCls:$,className:E,expandedKeys:d,selectedKeys:i,onSelect:y,onExpand:v}))},Eo=c.forwardRef(Co),No=Eo,It=zn;It.DirectoryTree=No;It.TreeNode=et;const Ko=It,wo="_container_vij12_2",ko="_title_vij12_35",$o="_icon_vij12_41",Oo="_info_vij12_52",Ro="_Xinfo_vij12_77",_o="_tabs_vij12_113",Do="_tit_vij12_35",Po="_banner_vij12_176",Mo="_scaleTransX_vij12_1",To="_dropButton_vij12_179",jo="_extra_vij12_219",ie={container:wo,"top-cont":"_top-cont_vij12_11","top-item":"_top-item_vij12_26",title:ko,icon:$o,info:Oo,"info-item":"_info-item_vij12_55",Xinfo:Ro,"contents-list":"_contents-list_vij12_102",tabs:_o,"tab-item":"_tab-item_vij12_121",tit:Do,"tabs-container":"_tabs-container_vij12_143","tab-slider":"_tab-slider_vij12_148","tab-active-item":"_tab-active-item_vij12_158",banner:Po,scaleTransX:Mo,dropButton:To,"courses-list":"_courses-list_vij12_204",extra:jo},Io="_item_70yav_1",Lo="_type_70yav_22",Bo="_info_70yav_59",Ho="_title_70yav_62",zo="_success_70yav_81",Be={item:Io,"top-content":"_top-content_70yav_18",type:Lo,"active-type":"_active-type_70yav_37","status-content":"_status-content_70yav_52",info:Bo,title:Ho,success:zo},Kt=({id:e,title:n,thumb:r,isRequired:t,progress:o})=>{const s=wn();return K.jsxs("div",{className:Be.item,onClick:()=>{s(`/course/${e}`)},children:[K.jsxs("div",{className:Be["top-content"],children:[K.jsx(Jt,{loading:"lazy",width:"100%",height:128,src:r,preview:!1}),t===1&&K.jsx("div",{className:Be.type,children:"必修"}),t===0&&K.jsx("div",{className:Be["active-type"],children:"选修课"})]}),K.jsxs("div",{className:Be["status-content"],children:[K.jsx("div",{className:Be.info,children:K.jsx("div",{className:Be.title,children:n})}),o==0&&K.jsxs(K.Fragment,{children:[K.jsx("span",{children:"学习进度：未学习"}),K.jsx(st,{style:{width:"100%"},percent:0,strokeColor:"rgba(230,151,36,1)",trailColor:"#F6F6F6",showInfo:!1,strokeLinecap:"butt"})]}),o>0&&o<100&&K.jsxs(K.Fragment,{children:[K.jsxs("span",{style:{color:"#606060"},children:["学习进度：",o,"%"]}),K.jsx(st,{percent:o,strokeColor:"rgba(230,151,36,1)",showInfo:!1,trailColor:"#F6F6F6",strokeLinecap:"butt"})]}),o>=100&&K.jsxs("div",{className:Be.success,children:[K.jsx(Jt,{loading:"lazy",width:24,height:24,src:wr,preview:!1}),K.jsx("span",{style:{color:"#606060"},children:"学习进度：已完成 100%"})]})]})]})},Ao="/assets/OBJECTS-2ba5e54d.png",Fo="/assets/OBJECTS-1-ea86cda6.png",Vo=()=>{const e=wn(),n=new URLSearchParams(xr().search),r=Zt(C=>C.systemConfig.value),[t,o]=c.useState(!1),[s,u]=c.useState(!1),[a,l]=c.useState(Number(n.get("tab")||0)),[i,f]=c.useState([]),[d,h]=c.useState([]),[v,y]=c.useState(Number(n.get("cid")||0)),[m,g]=c.useState(String(n.get("catName")||"所有分类")),[b,x]=c.useState([0]),[S,N]=c.useState({}),[k,$]=c.useState({}),[E,O]=c.useState(null),[p,D]=c.useState({}),P=Zt(C=>C.loginUser.value.currentDepId);c.useEffect(()=>{I()},[]),c.useEffect(()=>{let C=[];C.push(Number(n.get("cid")||0)),x(C)},[n.get("cid")]),c.useEffect(()=>{P!==0&&A()},[a,P,v]),c.useEffect(()=>{document.title=r.systemName||"首页"},[r]);const H=()=>{o(!1)},A=()=>{u(!0),Sr(P,v).then(C=>{const B=C.data.learn_course_records;if(O(C.data.stats),N(B),$(C.data.user_course_hour_count),D(C.data.resource_url),a===0)f(C.data.courses);else if(a===1){const M=[];C.data.courses.map(z=>{z.is_required===1&&M.push(z)}),f(M)}else if(a===2){const M=[];C.data.courses.map(z=>{z.is_required===0&&M.push(z)}),f(M)}else if(a===3){const M=[];C.data.courses.map(z=>{B[z.id]&&B[z.id].progress>=1e4&&M.push(z)}),f(M)}else if(a===4){const M=[];C.data.courses.map(z=>{(!B[z.id]||B[z.id]&&B[z.id].progress<1e4)&&M.push(z)}),f(M)}u(!1)})},I=()=>{Cr().then(C=>{const B=C.data.categories;if(JSON.stringify(B)!=="{}"){const M=L(B,0);M.unshift({key:0,title:"所有分类"}),h(M)}})},L=(C,B)=>{const M=[];for(let z=0;z<C[B].length;z++)if(!C[C[B][z].id])M.push({title:K.jsx("span",{style:{marginRight:20},children:C[B][z].name}),key:C[B][z].id});else{const he=L(C,C[B][z].id);M.push({title:K.jsx("span",{style:{marginRight:20},children:C[B][z].name}),key:C[B][z].id,children:he})}return M},j=[{key:0,label:"全部"},{key:1,label:"必修"},{key:2,label:"选修"},{key:3,label:"已学完"},{key:4,label:"未学完"}],V=C=>{console.log("items.findIndex(item => item.key === tabKey)",j.findIndex(B=>B.key===a)),l(C),e("/?cid="+v+"&catName="+m+"&tab="+C)},J=(C,B)=>{y(C[0]),B.node.key===0?(g(B.node.title),x(C),H(),e("/?cid="+C[0]+"&catName="+B.node.title+"&tab="+a)):(g(B.node.title.props.children),x(C),H(),e("/?cid="+C[0]+"&catName="+B.node.title.props.children+"&tab="+a))},re=C=>{o(C)},U=K.jsx("div",{style:{maxHeight:600,overflowX:"hidden",overflowY:"auto"},children:K.jsx(Ko,{selectedKeys:b,switcherIcon:null,onSelect:J,treeData:d,blockNode:!0,defaultExpandAll:!0})});return K.jsx("div",{className:"main-body",children:K.jsxs("div",{className:ie.container,children:[K.jsxs("div",{className:[ie["top-cont"],"page-menu"].join(" "),children:[K.jsxs("div",{className:`${ie["top-item"]} card-shadow`,children:[K.jsx("div",{className:ie.title,children:K.jsx("span",{children:"课程进度"})}),K.jsxs("div",{className:ie.info,children:[K.jsxs("div",{className:ie["info-item"],children:[K.jsxs("div",{children:[K.jsx("div",{style:{fontSize:"14px",color:"#000",marginBottom:10},children:"必修课"}),K.jsx("div",{style:{fontSize:"12px",color:"#606060"},children:"已学完"}),K.jsxs("strong",{children:[" ",(E==null?void 0:E.required_finished_course_count)||0," "]}),K.jsxs("span",{children:["/ ",(E==null?void 0:E.required_course_count)||0]})]}),K.jsx("div",{children:K.jsx(st,{strokeLinecap:"butt",type:"circle",percent:E&&E.required_course_count?Math.floor(E.required_finished_course_count/E.required_course_count*100):0,size:80,strokeWidth:20,strokeColor:"#22B938"})})]}),K.jsxs("div",{className:ie["info-item"],children:[K.jsxs("div",{children:[K.jsx("div",{style:{fontSize:"14px",color:"#000",marginBottom:10},children:"选修课"}),K.jsx("div",{style:{fontSize:"12px",color:"#606060"},children:"已学完"}),K.jsxs("strong",{style:{color:"#315782"},children:[" ",(E==null?void 0:E.nun_required_finished_course_count)||0," "]}),K.jsxs("span",{children:["/ ",(E==null?void 0:E.nun_required_course_count)||0]})]}),K.jsx("div",{children:K.jsx(st,{strokeLinecap:"butt",type:"circle",percent:E&&E.nun_required_course_count?Math.floor(E.nun_required_finished_course_count/E.nun_required_course_count*100):0,size:80,strokeWidth:20,strokeColor:"#315782"})})]})]}),K.jsx("img",{style:{marginTop:12},src:Ao,alt:""})]}),K.jsxs("div",{className:`${ie["top-item"]} card-shadow`,children:[K.jsx("div",{className:ie.title,children:K.jsx("span",{children:"学习时长"})}),E?K.jsxs("div",{className:ie.Xinfo,children:[K.jsxs("div",{className:ie["info-item"],children:[K.jsx("div",{style:{marginBottom:"10px"},children:"今日："}),K.jsx("div",{children:Xe(E.today_learn_duration)[0]!==0&&K.jsxs(K.Fragment,{children:[K.jsxs("strong",{children:[" ",Xe(E.today_learn_duration)[0]||0," "]}),"小时"]})}),K.jsxs("strong",{children:[" ",Xe(E.today_learn_duration)[1]||0," "]}),"分钟"]}),K.jsxs("div",{className:ie["info-item"],children:[K.jsx("div",{style:{marginBottom:"10px"},children:"累计"}),K.jsxs("div",{children:[Xe(E.learn_duration||0)[0]!==0&&K.jsxs(K.Fragment,{children:[K.jsxs("strong",{children:[" ",Xe(E.learn_duration||0)[0]||0," "]}),"小时"]}),K.jsxs("span",{children:[" ",Xe(E.learn_duration||0)[1]||0," "]}),"分钟"]})]})]}):null,K.jsx("img",{style:{marginTop:12},src:Fo,alt:""})]})]}),K.jsxs("div",{className:[ie["contents-list"],"page-menu"].join(" "),children:[K.jsxs("div",{className:ie.tabs,children:[j.map(C=>K.jsxs("div",{className:C.key===a?ie["tab-active-item"]:ie["tab-item"],onClick:()=>{V(C.key)},children:[K.jsx("div",{className:ie.tit,children:C.label}),C.key===0&&K.jsx("div",{className:ie["tab-slider"],style:{transform:`translateX(${a===0?24:a*(64+50)}px)`,transition:"transform 0.3s ease-in-out"}})]},C.key)),K.jsx(ea,{content:U,placement:"bottomRight",open:t,trigger:"click",onOpenChange:re,children:K.jsxs(Er,{className:ie.dropButton,children:[m,K.jsx("i",{className:"iconfont icon-icon-xiala",style:{fontSize:16}})]})})]}),s&&K.jsx(Qt,{style:{width:1344,margin:"0 auto",paddingTop:14,minHeight:301},children:K.jsx("div",{className:"float-left d-j-flex mt-50",children:K.jsx(Nr,{size:"large"})})}),!s&&i.length===0&&K.jsx(Qt,{style:{width:1344,margin:"0 auto",paddingTop:14,minHeight:301},children:K.jsx(Kr,{span:24,children:K.jsx(kr,{})})}),!s&&i.length>0&&K.jsx("div",{className:[ie["courses-list"],"page-menu"].join(" "),children:i.map(C=>K.jsxs(q.Fragment,{children:[S[C.id]&&K.jsx(Kt,{id:C.id,title:C.title,thumb:C.thumb===-1?yt:C.thumb===-2?mt:C.thumb===-3?bt:p[C.thumb],isRequired:C.is_required,progress:Math.floor(S[C.id].progress/100)}),!S[C.id]&&k[C.id]&&k[C.id]>0&&K.jsx(Kt,{id:C.id,title:C.title,thumb:C.thumb===-1?yt:C.thumb===-2?mt:C.thumb===-3?bt:p[C.thumb],isRequired:C.is_required,progress:1}),!S[C.id]&&!k[C.id]&&K.jsx(Kt,{id:C.id,title:C.title,thumb:C.thumb===-1?yt:C.thumb===-2?mt:C.thumb===-3?bt:p[C.thumb],isRequired:C.is_required,progress:0})]},C.id))})]})]})})};export{Vo as default};
