.container {
  display: flex;
  margin: 0 auto;
  max-width: 1600px;
  width: 100%;
  box-sizing: border-box;
  height: 100%;
}

.top-cont {
  width: 240px;
  height: auto;
  margin-right: 16px;
  // margin: 0 auto;
  padding-top: 24px;
  overflow-y: auto;
  // display: flex;
  // flex-direction: row;
  // align-items: center;
  // justify-content: space-between;
  -ms-overflow-style: none;
  /* 适用于 Internet Explorer 和旧版 Edge */
  scrollbar-width: none;
  /* 适用于 Firefox */

  .top-cont::-webkit-scrollbar {
    display: none;
    /* 隐藏滚动条 */
  }


  .top-item {
    width: 100%;
    // height: 132px;
    box-sizing: border-box;
    padding: 24px;
    border: 1px solid #dae1ed;
    border-radius: 4px;
    background-color: #fff;
    margin-bottom: 16px;

    .title {
      width: 100%;
      height: 36px;
      display: flex;
      align-items: center;

      .icon {
        width: 36px;
        height: 36px;
        margin-right: 16px;
      }

      span {
        font-size: 20px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.88);
        line-height: 36px;
      }
    }

    .info {
      width: 100%;

      .info-item {
        width: 100%;
        margin-top: 12px;
        font-size: 14px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.45);
        line-height: 24px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        &:last-child {
          margin-right: 0px;
        }

        strong {
          font-size: 24px;
          color: #22B938;
        }

        span {
          color: #606060;
          font-size: 12px;
        }
      }
    }

    .Xinfo {
      width: 100%;
      display: flex;
      align-items: center;

      .info-item {
        width: 100%;
        margin-top: 12px;
        font-size: 14px;
        font-weight: 600;
        color: #252A2F;
        text-align: left;

        &:last-child {
          margin-right: 0px;
        }

        strong {
          font-size: 24px;
          color: #E69724;
        }

        span {
          font-size: 24px;
          color: #315782;
        }
      }
    }
  }
}

.contents-list {
  background: #ffffff;
  border: 1px solid #dae1ed;
  margin-top: 24px;
  border-radius: 4px;
  flex: 1;
  min-width: 0;
  // padding: 0 24px 24px 24px;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;

  .tabs {
    // width: 1200px;
    height: 48px;
    display: flex;
    align-items: center;
    position: relative;
    border-bottom: 1px solid #dae1ed;
    padding-left: 24px;

    .tab-item {
      width: 64px;
      height: 48px;
      margin-right: 50px;
      transition: all 0.2s;
      position: relative;
      cursor: pointer;

      &:hover {
        opacity: 0.8;

        .tit {
          color: #66B821;
        }
      }

      .tit {
        width: 64px;
        height: 48px;
        font-size: 16px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.88);
        line-height: 48px;
      }
    }

    .tabs-container {
      position: relative;
      display: inline-flex;
      margin-right: 30px;
    }

    .tab-slider {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 64px;
      height: 1px;
      background-color: #66B821; // 你可以根据需要调整颜色
      transition: transform 0.3s ease-in-out;
      transform: translateX(0);
    }

    .tab-active-item {
      width: 64px;
      height: 48px;
      cursor: pointer;
      margin-right: 50px;
      transition: all 0.2s;

      &:hover {
        opacity: 0.8;
      }

      .tit {
        width: 64px;
        height: 48px;
        font-size: 16px;
        color: #66b821;
        font-weight: 600;
        line-height: 48px;
      }

      .banner {
        animation: scaleTransX 0.3s;
      }
    }

    .dropButton {
      height: 40px;
      font-size: 14px;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.45);
      line-height: 40px;
      display: flex;
      align-items: center;
      position: absolute;
      right: 24px;
      top: 4px;
      cursor: pointer;
    }
  }
}



@keyframes scaleTransX {
  0% {
    transform: translateX(0px);
  }

  50% {
    transform: translateX(6px);
  }

  100% {
    transform: translateX(0px);
  }
}

.courses-list {
  height: calc(100% - 48px);
  margin: 0 auto;
  box-sizing: border-box;
  // padding-top: 24px;
  // display: grid;
  // gap: 24px;
  // grid-template-columns: repeat(3, minmax(0, 1fr));
  display: flex;
  flex-wrap: wrap;
  gap: 14px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  padding: 16px;
  align-content: flex-start;
  row-gap: 14px;

}


.extra {
  width: 1200px;
  margin: 0 auto;
  margin-top: 80px;
  text-align: center;
  height: 40px;
  font-size: 16px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.2);
  line-height: 40px;
}