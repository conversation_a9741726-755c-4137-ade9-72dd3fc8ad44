import{W as R,X as s,a8 as S,a9 as W,$ as q,r,a5 as D,a6 as P,Y as g,Z as v}from"./index-d424f8d9.js";import{d as G,a as K}from"./course-e91cc22f.js";import{d as Z,a as Y,b as V,m as $,E as ee}from"./thumb3-b5583002.js";const se="_info_w6e90_21",te="_title_w6e90_30",ae="_status_w6e90_41",ce="_type_w6e90_47",ie="_success_w6e90_73",ne="_desc_w6e90_86",re="_tabs_w6e90_103",oe="_tit_w6e90_30",de="_banner_w6e90_152",le="_scaleTransX_w6e90_1",he="_download_w6e90_241",t={"course-container":"_course-container_w6e90_1","top-cont":"_top-cont_w6e90_12",info:se,title:te,status:ae,type:ce,"active-type":"_active-type_w6e90_60",success:ie,desc:ne,tabs:re,"tab-item":"_tab-item_w6e90_112",tit:oe,"tab-active-item":"_tab-active-item_w6e90_134",banner:de,scaleTransX:le,"chapters-hours-cont":"_chapters-hours-cont_w6e90_156","hours-list-box":"_hours-list-box_w6e90_166","chapter-it":"_chapter-it_w6e90_172","chapter-name":"_chapter-name_w6e90_180","hours-it":"_hours-it_w6e90_190","attachments-cont":"_attachments-cont_w6e90_199","attachments-item":"_attachments-item_w6e90_209","left-cont":"_left-cont_w6e90_224",download:he},ue="_item_d0hxa_1",_e="_title_d0hxa_19",me="_complete_d0hxa_22",xe="_record_d0hxa_47",je="_link_d0hxa_55",d={item:ue,"left-item":"_left-item_d0hxa_16",title:_e,complete:me,record:xe,link:je},f=({id:u,cid:w,title:N,duration:x,record:_,progress:a})=>{const b=R();return s.jsx(s.Fragment,{children:s.jsxs("div",{className:d.item,onClick:()=>{b(`/course/${w}/hour/${u}`)},children:[s.jsxs("div",{className:d["left-item"],children:[s.jsx("i",{className:"iconfont icon-icon-video"}),s.jsxs("div",{className:d.title,children:[N,"(",S(Number(x)),")"]})]}),s.jsxs("div",{className:"d-flex",children:[a>=0&&a<100&&s.jsxs(s.Fragment,{children:[a===0&&s.jsx("div",{className:d.link,children:"开始学习"}),a!==0&&s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:d.record,children:["上次学习到",S(Number(_.finished_duration||0))]}),s.jsx("div",{className:d.link,children:"继续学习"})]})]}),a>=100&&s.jsx("div",{className:d.complete,children:"已学完"})]})]})})},pe="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAAAQCAYAAACBSfjBAAAAAXNSR0IArs4c6QAAAwtJREFUWEftl09rVGcUxn/PnekEU7GgqYi04L9SV00qulIcBWuZkDuZUrKw0IqfoFC76yaLLmuhn0DUhQsRxhnJoBbqFLpqsUlXKbZaaBHxT6GSWjLO3EfuTTqEGMebiZUYe+HwXu4957zn/Hh5zzkCcDj8AfA58CrmPIGPqVL5If73/zNDwMXiTiIdRQwBt0GfqVo+LQ+9twdFdSCYB+sbzBecP1cT+EUEaWJcwwXEp8D+eQwiHOTlcPgCcLADoEnk4zSbJzU2dvNFAOnBwQ1ksx9hHQG2d8j5ojxUnEJ6OQWYJlADnYBWTdXq/RQ2z42Kw7AXMgXwYaAAZFMEPxWfwCkgDcC5/v7BXALKKKqqWr2TYrNlp+Iw7MNBCJQQ7wCrFhnk3/LQ8CXEgUUazlVvAT+B6xDUabz0rS6c+XMJ/v4zU787spbcg70Q5UF54C0g0/WG5utORaRbvxFwFRjHjKNoAmlClcqNbh12Y+dicSN2Pw76EQOQyBsLFMtu3Mc2M0UkKdFh6RD4S2BDt95S2MV35m/AdazrBPwOuoO52xZl79GiwWoa0JbYdS6RKXJkyOHmGsS6tuA+Il5H3gzEsgnoTRFTtyo3QZ8kbcy/Hlwo9JDLfUjE0SdUnm43XQl2kwQco9E4pVptOk6oDbANMv4WhkUIPsbeh/SIzkogkToH20iXIfqKarUyvyfuCMel0lZaPoI4jHkt9aYrQVH8gTlBRsdVLv/6uJRSnS6PjgZcuXIQ632gCKxfCYwWyOEWUEE+y44dFzU6GhfEjk8qgHM9JDC//3E3GZWwB4E3F7oKnrTxMvkfj6g/I43Rcpldb3+XBtrc2BcNcH7iydij7D4yymPy2NuX7b05c59NIuq0XMfNy0sdT5cM8BGghcIaglw/gQZwNIDUj7QN+5Vneuqkv7B/wZ5AwTiRx4kaE6rV7j3NOJ46wMcF55GRtUxPb0n6tIhNSPE92gesS1Ynay+a7fmgZ7b/i13GfWHcNjRw8n4fcReIR8iZ1b5FMNtn9vRc05lnMw09BFOnCpbDccvhAAAAAElFTkSuQmCC",be=()=>{const u=W(),w=R(),N=new URLSearchParams(q().search),[x,_]=r.useState(!0),[a,b]=r.useState(null),[j,C]=r.useState([]),[l,U]=r.useState({}),[n,E]=r.useState(null),[i,T]=r.useState({}),[p,B]=r.useState(Number(N.get("tab")||1)),[F,L]=r.useState([]),[I,O]=r.useState({}),[Q,J]=r.useState([]);r.useEffect(()=>{X()},[u.courseId]);const X=()=>{_(!0),G(Number(u.courseId)).then(e=>{document.title=e.data.course.title,b(e.data.course),C(e.data.chapters),U(e.data.hours),O(e.data.resource_url),e.data.learn_record&&E(e.data.learn_record),e.data.learn_hour_records&&T(e.data.learn_hour_records);let o=e.data.attachments,c=[{key:1,label:"课程目录"}];o.length>0&&(c.push({key:2,label:"课程附件"}),L(o)),J(c),_(!1)}).catch(e=>{_(!1)})},z=e=>{B(e),w("/course/"+u.courseId+"?tab="+e)},H=(e,o,c,y,M)=>{K(e,o).then(A=>{M==="TXT"?fetch(A.data.resource_url[c]).then(m=>m.blob()).then(m=>{const k=URL.createObjectURL(m),h=document.createElement("a");h.style.display="none",h.href=k,h.download=y,document.body.appendChild(h),h.click(),URL.revokeObjectURL(k),document.body.removeChild(h)}).catch(m=>{console.error("下载文件时出错:",m)}):window.open(A.data.resource_url[c])})};return s.jsxs("div",{className:t["course-container"],children:[x&&s.jsx(D,{style:{width:1600,margin:"0 auto",paddingTop:14,minHeight:301},children:s.jsx("div",{className:"float-left d-j-flex mt-50",children:s.jsx(P,{size:"large"})})}),!x&&s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:t["top-cont"],children:[s.jsxs("div",{className:"j-b-flex",children:[s.jsxs("div",{className:"d-flex",children:[a?s.jsx(g,{width:120,height:90,style:{borderRadius:10},preview:!1,src:a.thumb===-1?Z:a.thumb===-2?Y:a.thumb===-3?V:I[a.thumb]}):null,s.jsxs("div",{className:t.info,children:[s.jsx("div",{className:t.title,children:a==null?void 0:a.title}),s.jsxs("div",{className:t.status,children:[(a==null?void 0:a.is_required)===1&&s.jsx("div",{className:t.type,children:"必修"}),(a==null?void 0:a.is_required)===0&&s.jsx("div",{className:t["active-type"],children:"选修"}),n&&n.progress/100>=100&&s.jsxs("div",{className:t.success,children:[s.jsx(g,{width:24,height:24,src:$,preview:!1}),s.jsx("span",{className:"ml-8",children:"恭喜你学完此课程!"})]})]})]})]}),(!n||n&&JSON.stringify(n)==="{}")&&JSON.stringify(i)==="{}"&&s.jsx(v,{strokeLinecap:"butt",type:"circle",percent:0,format:e=>`${e}%`,size:80,strokeWidth:20,strokeColor:"#22B938"}),(!n||n&&JSON.stringify(n)==="{}")&&JSON.stringify(i)!=="{}"&&s.jsx(v,{strokeLinecap:"butt",type:"circle",percent:1,format:e=>`${e}%`,size:80,strokeWidth:20,strokeColor:"#22B938"}),n&&JSON.stringify(n)!=="{}"&&JSON.stringify(i)!=="{}"&&s.jsx(v,{strokeLinecap:"butt",type:"circle",percent:Math.floor(n.progress/100),format:e=>`${e}%`,size:80,strokeWidth:20,strokeColor:"#22B938"})]}),(a==null?void 0:a.short_desc)&&s.jsx("div",{className:t.desc,children:a.short_desc})]}),s.jsx("div",{className:t.tabs,children:Q.map(e=>s.jsxs("div",{className:e.key===p?t["tab-active-item"]:t["tab-item"],onClick:()=>{z(e.key)},children:[s.jsx("div",{className:t.tit,children:e.label}),e.key===p&&s.jsx(g,{className:t.banner,width:40,height:8,preview:!1,src:pe,style:{marginTop:-16}})]},e.key))}),p===1&&s.jsxs("div",{className:t["chapters-hours-cont"],children:[j.length===0&&JSON.stringify(l)==="{}"&&s.jsx(ee,{}),j.length===0&&JSON.stringify(l)!=="{}"&&s.jsx("div",{className:t["hours-list-box"],children:l[0].map((e,o)=>s.jsxs("div",{className:t["hours-it"],children:[i[e.id]&&s.jsx(f,{id:e.id,cid:e.course_id,title:e.title,record:i[e.id],duration:e.duration,progress:i[e.id].finished_duration*100/i[e.id].total_duration}),!i[e.id]&&s.jsx(f,{id:e.id,cid:e.course_id,title:e.title,record:null,duration:e.duration,progress:0})]},e.id))}),j.length>0&&JSON.stringify(l)!=="{}"&&s.jsx("div",{className:t["hours-list-box"],children:j.map((e,o)=>s.jsxs("div",{className:t["chapter-it"],children:[s.jsx("div",{className:t["chapter-name"],children:e.name}),l[e.id]&&l[e.id].map((c,y)=>s.jsxs("div",{className:t["hours-it"],children:[i[c.id]&&s.jsx(f,{id:c.id,cid:e.course_id,title:c.title,record:i[c.id],duration:c.duration,progress:i[c.id].finished_duration*100/i[c.id].total_duration}),!i[c.id]&&s.jsx(f,{id:c.id,cid:e.course_id,title:c.title,record:null,duration:c.duration,progress:0})]},c.id))]},e.id))})]}),p===2&&s.jsx("div",{className:t["attachments-cont"],children:F.map((e,o)=>s.jsxs("div",{className:t["attachments-item"],children:[s.jsxs("div",{className:t["left-cont"],children:[s.jsx("i",{className:"iconfont icon-icon-file",style:{fontSize:16,color:"rgba(0,0,0,0.3)",marginRight:10}}),s.jsxs("span",{className:t.title,children:[e.title,".",e.ext]})]}),s.jsx("div",{className:t.download,onClick:()=>H(e.course_id,e.id,e.rid,`${e.title}.${e.ext}`,e.type),children:"下载"})]},o))})]})]})};export{be as default};
