import React, { useState, useEffect } from "react";
import { Image, Progress } from "antd";
import { useNavigate } from "react-router-dom";
import styles from "./courses-model.module.scss";
import mediaIcon from "../../../assets/images/commen/icon-medal.png";

interface PropInterface {
  id: number;
  title: string;
  thumb: string;
  isRequired: number;
  progress: number;
}

export const CoursesModel: React.FC<PropInterface> = ({
  id,
  title,
  thumb,
  isRequired,
  progress,
}) => {
  const navigate = useNavigate();
  return (
    <div
      className={styles["item"]}
      onClick={() => {
        navigate(`/course/${id}`);
      }}
    >
      <div className={styles["top-content"]}>
        <Image
          loading="lazy"
          width={'100%'}
          height={128}
          src={thumb}
          preview={false}
        />
        {isRequired === 1 && <div className={styles["type"]}>必修</div>}
        {isRequired === 0 && (
          <div className={styles["active-type"]}>选修课</div>
        )}
      </div>
      <div className={styles["status-content"]}>
        <div className={styles["info"]}>
          <div className={styles["title"]}>{title}</div>
        </div>
        {progress == 0 && (
          <>
            <span>学习进度：未学习</span>
            <Progress
              style={{ width: '100%' }}
              percent={0}
              strokeColor="rgba(230,151,36,1)"
              trailColor="#F6F6F6"
              showInfo={false}
              strokeLinecap="butt"
            />
          </>
        )}
        {progress > 0 && progress < 100 && (
          <>
            <span style={{ color: '#606060' }}>学习进度：{progress}%</span>
            <Progress
              percent={progress}
              strokeColor="rgba(230,151,36,1)"
              showInfo={false}
              trailColor="#F6F6F6"
              strokeLinecap="butt"
            />
          </>
        )}
        {progress >= 100 && (
          <div className={styles["success"]}>
            <Image
              loading="lazy"
              width={24}
              height={24}
              src={mediaIcon}
              preview={false}
            />
            <span style={{ color: '#606060' }}>学习进度：已完成 100%</span>
          </div>
        )}
      </div>
    </div>
  );
};
