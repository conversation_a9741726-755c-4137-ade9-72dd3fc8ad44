import{r as t,N as K,b as Q,W as Y,a9 as Z,a0 as L,aa as ee,ab as E,ac as te,X as s}from"./index-d424f8d9.js";import{d as ae,p as re,b as se,r as le,c as ne}from"./course-e91cc22f.js";var oe={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 000 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"arrow-left",theme:"outlined"};const ce=oe;var de=function(a,o){return t.createElement(K,Q({},a,{ref:o,icon:ce}))},ie=t.forwardRef(de);const ue=ie,me="_box_1g44j_21",n={"video-mask":"_video-mask_1g44j_1","top-cont":"_top-cont_1g44j_11",box:me,"close-btn":"_close-btn_1g44j_28","video-body":"_video-body_1g44j_42","video-title":"_video-title_1g44j_52","video-box":"_video-box_1g44j_62","alert-message":"_alert-message_1g44j_69","des-video":"_des-video_1g44j_86","alert-button":"_alert-button_1g44j_92"};var c=null;const we=()=>{const p=Y(),a=Z(),o=L(e=>e.systemConfig.value),v=L(e=>e.loginUser.value.user),[pe,T]=t.useState(""),[b,U]=t.useState(0),[A,_]=t.useState(!1),[fe,w]=t.useState({}),[ye,B]=t.useState(null),[d,F]=t.useState(null),[H,f]=t.useState(!1),[x,I]=t.useState(!1),[y,S]=t.useState([]),[N,j]=t.useState(0),[P,C]=t.useState(0),[he,O]=t.useState({}),k=t.useRef(0),h=t.useRef(0),i=t.useRef(0),g=t.useRef(0),[$,R]=t.useState(!1);t.useEffect(()=>(c&&clearInterval(c),z(),V(),()=>{c&&clearInterval(c)}),[a.courseId,a.hourId]),t.useEffect(()=>{k.current=b},[b]),t.useEffect(()=>{h.current=N},[N]),t.useEffect(()=>{i.current=P},[P]),t.useEffect(()=>{g.current=(d==null?void 0:d.duration)||0},[d]);const z=()=>{ae(Number(a.courseId)).then(e=>{let r=[];if(e.data.chapters.length===0)S(e.data.hours[0]),r=e.data.hours[0];else if(e.data.chapters.length>0){const u=[];for(let m in e.data.hours)e.data.hours[m].map(J=>{u.push(J)});S(u),r=u}r.findIndex(u=>u.id===Number(a.hourId))===r.length-1&&I(!0)})},V=()=>{if(H)return!0;f(!0),re(Number(a.courseId),Number(a.hourId)).then(e=>{B(e.data.course),F(e.data.hour),document.title=e.data.hour.title;let r=e.data.user_hour_record,l=null;r&&r.finished_duration&&r.is_finished===0?(l={time:5,pos:r.finished_duration},w(l),w(l),C(r.finished_duration)):r&&r.is_finished===1&&C(e.data.hour.duration),W(e.data.hour.rid,l),f(!1)}).catch(e=>{f(!1)})},W=(e,r)=>{se(Number(a.courseId),Number(a.hourId)).then(l=>{O(l.data.resource_url[e]),T(l.data.resource_url[e]),M(l.data.resource_url[e],0,r),ee(String(a.courseId)+"-"+String(a.hourId))})},M=(e,r,l)=>{let u=o.playerIsDisabledDrag&&i.current<g.current&&i.current===0;window.player=new window.DPlayer({container:document.getElementById("meedu-player-container"),autoplay:!1,video:{url:e,pic:o.playerPoster},try:r===1,bulletSecret:{enabled:o.playerIsEnabledBulletSecret,text:o.playerBulletSecretText.replace("{name}",v.name).replace("{email}",v.email).replace("{idCard}",v.id_card),size:"14px",color:o.playerBulletSecretColor||"red",opacity:Number(o.playerBulletSecretOpacity)},ban_drag:u,last_see_pos:l}),window.player.on("timeupdate",()=>{let m=parseInt(window.player.video.currentTime);o.playerIsDisabledDrag&&i.current<g.current&&m-h.current>=2&&m>i.current?(E.warning("首次学习禁止快进"),window.player.seek(i.current)):(j(m),D(parseInt(window.player.video.currentTime),!1))}),window.player.on("ended",()=>{if(o.playerIsDisabledDrag&&i.current<g.current&&window.player.video.duration-h.current>=2){window.player.seek(h.current);return}j(0),_(!0),D(parseInt(window.player.video.currentTime),!0),G(),window.player&&window.player.destroy()}),f(!1),X()},D=(e,r)=>{(e-k.current>=10||r===!0)&&(U(e),le(Number(a.courseId),Number(a.hourId),e).then(l=>{}),ne(Number(a.courseId),Number(a.hourId)).then(l=>{}))},X=()=>{c=setInterval(()=>{let e=te();e&&e!==String(a.courseId)+"-"+String(a.hourId)?(c&&clearInterval(c),window.player&&window.player.destroy(),R(!0)):R(!1)},5e3)},q=()=>{const e=y.findIndex(r=>r.id===Number(a.hourId));e===y.length-1?(I(!0),E.error("已经是最后一节了！")):e<y.length-1&&p(`/course/${a.courseId}/hour/${y[e+1].id}`,{replace:!0})},G=()=>{let e;e=document,e.fullscreenElement!==null?e.exitFullscreen():e.mozCancelFullScreen?e.mozCancelFullScreen():e.webkitCancelFullScreen&&e.webkitCancelFullScreen()};return s.jsxs("div",{className:n["video-mask"],children:[s.jsx("div",{className:n["top-cont"],children:s.jsx("div",{className:n.box,children:s.jsxs("div",{className:n["close-btn"],onClick:()=>{c&&clearInterval(c),window.player&&window.player.destroy(),p(-1)},children:[s.jsx(ue,{}),s.jsx("span",{className:"ml-14",children:"返回"})]})})}),s.jsxs("div",{className:n["video-body"],children:[s.jsx("div",{className:n["video-title"],children:d==null?void 0:d.title}),s.jsxs("div",{className:n["video-box"],children:[s.jsx("div",{className:"play-box",id:"meedu-player-container",style:{borderRadius:8}}),$&&s.jsx("div",{className:n["alert-message"],children:s.jsx("div",{className:n["des-video"],children:"您已打开新视频，暂停本视频播放"})}),A&&s.jsxs("div",{className:n["alert-message"],children:[x&&s.jsx("div",{className:n["alert-button"],onClick:()=>p(`/course/${a.courseId}`),children:"恭喜你学完最后一节"}),!x&&s.jsx("div",{className:n["alert-button"],onClick:()=>{window.player&&window.player.destroy(),w({}),_(!1),q()},children:"播放下一节"})]})]})]})]})};export{we as default};
