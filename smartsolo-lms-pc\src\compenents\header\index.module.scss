.app-header {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 50px;
  background: #1B2534;
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.04);
  z-index: 100;
  padding: 0 24px;
  box-sizing: border-box;

  .main-header {
    max-width: 1600px;
    width: 100%;
    height: 50px;
    line-height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 auto;
    // padding: 0 24px;
    box-sizing: border-box;

    .App-logo {
      width: 124px;
      height: 40px;
      text-align: left;

      img {
        width: auto;
        height: 40px;
      }
    }

    .navs {
      display: flex;
      align-items: center;
      overflow: hidden;
      height: 50px;

      .nav-item {
        width: auto;
        font-size: 14px;
        height: 28px;
        line-height: 28px;
        padding: 0 8px;
        font-weight: 400;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 56px;
        cursor: pointer;

        &:hover {
          opacity: 0.8;
          color: #66B821;
        }
      }

      .nav-active-item {
        height: 28px;
        line-height: 28px;
        padding: 0 8px;
        font-size: 14px;
        font-weight: 600;
        display: initial;
        color: #66B821;
        background: rgba(255, 255, 255, 0.10);
        border-radius: 4px;
        margin-left: 56px;
        cursor: pointer;

        &:hover {
          opacity: 0.8;
        }
      }
    }

    .department-name {
      font-size: 14px;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.45);
      line-height: 24px;
      background: #f6f6f6;
      border-radius: 8px 4px 8px 4px;
      padding: 0 8px;
      margin-right: 16px;
    }
  }
}