$primaryColor: #66B821;

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}

a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  // place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

.layout-box {
  display: flex;
  flex-direction: column;
  height: 100vh;
}


.footer-box {
  flex: 1;
  display: flex;
  flex-direction: column-reverse;
}

.main-body {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
  // padding-top: 60px;
  background-color: #F5F7FA;
  padding: 50px 24px 24px 24px;
  overflow: hidden;
}

.container {
  max-width: 1600px;
  width: 100%;
  // min-width: 0;
  height: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding-top: 60px;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }

  a:hover {
    color: #747bff;
  }

  button {
    background-color: #f9f9f9;
  }
}

.w-250px {
  width: 250px;
}

.w-300px {
  width: 300px;
}

.w-350px {
  width: 350px;
}

.w-400px {
  width: 200px;
}

.w-450px {
  width: 200px;
}

.w-500px {
  width: 200px;
}

.mr-5 {
  margin-right: 5px;
}

.ml-5 {
  margin-left: 5px;
}

.ml-8 {
  margin-left: 8px;
}

.mt-10 {
  margin-top: 10px;
}

.ml-14 {
  margin-left: 14px;
}

.ml-15 {
  margin-left: 15px;
}

.ml-120 {
  margin-left: 120px;
}

.ml-16 {
  margin-left: 16px;
}

.mr-16 {
  margin-right: 16px;
}

.mb-8 {
  margin-bottom: 8px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mt-24 {
  margin-top: 24px;
}

.mb-24 {
  margin-bottom: 24px;
}

.mr-24 {
  margin-right: 24px;
}

.mb-28 {
  margin-bottom: 28px;
}

.mt-50 {
  margin-top: 50px;
}

.mb-50 {
  margin-bottom: 50px;
}

.helper-text {
  height: 24px;
  font-size: 12px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.45);
  line-height: 24px;
}

.float-left {
  width: 100%;
  height: auto;
  float: left;
}

.d-flex {
  display: flex;
  align-items: center;
}

.j-flex {
  display: flex;
  justify-content: center;
}

.d-j-flex {
  display: flex;
  align-items: center;
  justify-content: center;
}

.j-r-flex {
  display: flex;
  justify-content: right;
}

.j-b-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.c-flex {
  display: flex;
  flex-direction: column;
}

.c-a-flex {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.flex-1 {
  flex: 1;
}

.primary {
  color: $primaryColor;
}

.c-yellow {
  color: #e1a500;
}

.c-success {
  color: #04c877;
}

.c-green {
  color: #00cc66;
}

.c-red {
  color: $primaryColor;
}


.card-shadow {
  transition: transform 0.3s ease, box-shadow 0.3s ease; // 添加过渡动画

  &:hover {
    transform: translateY(-5px); // 向上移动5像素
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); // 添加阴影效果
  }
}

.page-menu {
  transform: translateX(-100%);
  animation: menuEnter 0.5s ease forwards;

  /* 菜单主体进入动画 */
  @keyframes menuEnter {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
}


.login-box {
  .ant-btn {
    font-size: 18px !important;
    font-weight: 500 !important;
  }

  .ant-input,
  .ant-input-password {
    font-weight: 400;
    font-size: 18px !important;
    background-color: #f6f6f6;

    &::placeholder {
      color: rgba(0, 0, 0, 0.45);
    }
  }
}

.c-admin {
  font-size: 14px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.88);
  line-height: 24px;
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }
}

.ant-progress {
  margin-bottom: 0px;

  .ant-progress-text {
    color: #252A2F !important;
  }
}

.ant-modal-confirm-btns>.ant-btn-default:hover {
  color: #66B821 !important;
  border-color: #66B821 !important;
}

.ant-modal-confirm-btns>.ant-btn-primary {
  background-color: #66B821 !important;
  color: #fff;

  &:hover {
    opacity: 0.8;
  }
}

.ant-dropdown-menu-item-group-list {
  padding: 0 0 4px 0 !important;
}

#meedu-player-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
}

.ant-tree-switcher {
  display: none !important;
}

.ant-tree-node-selected {
  .ant-tree-title {
    color: #ff4d4f;
  }
}

.ant-tree-treenode {
  min-width: 150px;
  width: 100% !important;
  height: 40px !important;
  padding: 0 !important;
  display: flex;
  align-items: center !important;

  &.ant-tree-treenode-selected {
    background-color: #fff2f0 !important;
    border-radius: 0px !important;
    overflow: hidden;

    .ant-tree-node-content-wrapper {
      background-color: transparent !important;
    }

    .ant-tree-node-content-wrapper-normal {
      background-color: transparent !important;
    }
  }

  .ant-tree-node-content-wrapper {
    width: 100% !important;
    height: 40px !important;
    display: flex;
    align-items: center;
  }

  &:hover {
    background-color: #fff2f0 !important;
    border-radius: 0px !important;
    overflow: hidden;

    .ant-tree-node-content-wrapper {
      background-color: transparent !important;
    }

    .ant-tree-node-content-wrapper-normal {
      background-color: transparent !important;
    }
  }

  .ant-tree-node-content-wrapper-normal {
    width: 100% !important;
    height: 40px !important;
    display: flex;
    align-items: center;
  }

  .ant-tree-switcher {
    height: 40px !important;
    display: flex;
    align-items: center;
  }
}

.ant-tree-title {
  flex: 1;

  padding-left: 20px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .iconfont {
    color: rgba(0, 0, 0, 0.2);
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }
}

.ant-popover-inner {
  padding: 8px 0px !important;
}

.dplayer-notice {
  opacity: 0 !important;
}