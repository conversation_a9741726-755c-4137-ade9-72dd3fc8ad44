{"name": "smartsolo-lms-pc", "private": false, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode dev", "build": "tsc && vite build --mode prod", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.3.0", "@reduxjs/toolkit": "^1.9.3", "add": "^2.0.6", "antd": "^5.3.2", "axios": "^1.3.4", "dayjs": "^1.11.10", "localforage": "^1.10.0", "match-sorter": "^6.3.1", "moment": "^2.29.4", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^8.0.5", "react-router-dom": "^6.9.0", "redux": "^4.2.1", "sort-by": "^1.2.0"}, "devDependencies": {"@types/node": "^24.3.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-legacy": "^4.1.1", "@vitejs/plugin-react-swc": "^3.0.0", "rollup-plugin-gzip": "^3.1.0", "rollup-plugin-terser": "^7.0.2", "sass": "^1.59.3", "terser": "^5.20.0", "typescript": "^4.9.3", "vite": "^4.2.0"}}