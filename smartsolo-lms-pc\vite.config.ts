import { defineConfig, loadEnv } from "vite";
import path from 'path';
import react from "@vitejs/plugin-react-swc";
// import gzipPlugin from "rollup-plugin-gzip";
// import legacy from "@vitejs/plugin-legacy";
// import { terser } from "rollup-plugin-terser";

// https://vitejs.dev/config/

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  return {
    server: {
      host: "0.0.0.0",
      port: 9797,
      proxy: {
        '/api': {
          target: env.VITE_API_BASE_PATH,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        }
      },
    },
    plugins: [
      react(),
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
      },
    },
    build: {
      outDir: env.VITE_OUT_DIR || 'dist',
      // rollupOptions: {
      //   plugins: [
      //     terser({
      //       compress: {
      //         drop_console: env.VITE_DISABLE_LOG === 'true',
      //         drop_debugger: true,
      //       },
      //     }),
      //   ],
      // },
    },
    css: {
      preprocessorOptions: {
        scss: {
          silenceDeprecations: ['legacy-js-api']
        }
      }
    },
  };
});
