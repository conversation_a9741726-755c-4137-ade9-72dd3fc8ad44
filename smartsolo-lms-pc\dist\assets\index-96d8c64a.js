import{W as j,a0 as m,r as a,X as e,a5 as c,a6 as g,a7 as v,Y as i,Z as n}from"./index-d424f8d9.js";import{l as f}from"./course-e91cc22f.js";import{E as y,d as F,a as N,b,m as C}from"./thumb3-b5583002.js";const w="_content_eeai6_2",E="_item_eeai6_19",S="_type_eeai6_38",L="_top_eeai6_73",k="_title_eeai6_79",z="_record_eeai6_92",R="_progress_eeai6_105",T="_tip_eeai6_112",q="_extra_eeai6_121",r={content:w,item:E,type:S,"active-type":"_active-type_eeai6_53","item-info":"_item-info_eeai6_68",top:L,title:k,record:z,progress:R,tip:T,extra:q},U=()=>{document.title="最近学习";const d=j();m(s=>s.systemConfig.value);const[t,l]=a.useState(!1),[o,u]=a.useState([]),[_,h]=a.useState({});a.useEffect(()=>{p()},[]);const p=()=>{l(!0),f().then(s=>{s.data.resource_url&&s.data.user_latest_learns&&(h(s.data.resource_url),u(s.data.user_latest_learns)),l(!1)})};return e.jsx("div",{className:"main-body",children:e.jsxs("div",{className:[r.content,"page-menu"].join(" "),children:[t&&e.jsx(c,{style:{width:1600},children:e.jsx("div",{className:"float-left d-j-flex mt-50",children:e.jsx(g,{size:"large"})})}),!t&&o.length===0&&e.jsx(c,{style:{width:1600},children:e.jsx(v,{span:24,children:e.jsx(y,{})})}),!t&&o.length>0&&o.map((s,x)=>e.jsx("div",{children:s.course&&e.jsxs("div",{className:r.item,onClick:()=>{d(`/course/${s.course.id}`)},children:[e.jsxs("div",{style:{width:344},children:[e.jsx(i,{loading:"lazy",src:s.course.thumb===-1?F:s.course.thumb===-2?N:s.course.thumb===-3?b:_[s.course.thumb],width:344,height:128,preview:!1}),s.course.is_required===1&&e.jsx("div",{className:r.type,children:"必修"}),s.course.is_required===0&&e.jsx("div",{className:r["active-type"],children:"选修"})]}),e.jsxs("div",{className:r["item-info"],children:[e.jsx("div",{className:r.top,children:e.jsx("div",{className:r.title,children:s.course.title})}),s.record&&e.jsxs(e.Fragment,{children:[s.last_learn_hour&&e.jsxs("div",{className:r.record,children:["上次学到：",s.last_learn_hour.title]}),e.jsxs("div",{className:r.progress,children:[s.record.progress<1e4&&e.jsx(n,{percent:Math.floor(s.record.progress/100),strokeColor:"#FF4D4F",trailColor:"#F6F6F6"}),s.record.progress>=1e4&&e.jsxs(e.Fragment,{children:[e.jsx(i,{loading:"lazy",width:24,height:24,src:C,preview:!1}),e.jsx("span",{className:r.tip,children:"恭喜你学完此课程!"})]})]})]}),!s.record&&e.jsxs(e.Fragment,{children:[s.last_learn_hour&&e.jsxs("div",{className:r.record,children:["上次学到：",s.last_learn_hour.title]}),e.jsx("div",{className:r.progress,children:e.jsx(n,{percent:1,strokeColor:"#FF4D4F",trailColor:"#F6F6F6"})})]})]})]})},x))]})})};export{U as default};
